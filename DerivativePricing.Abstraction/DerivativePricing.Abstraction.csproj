<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>DerivativePricing</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <PlatformTarget>x64</PlatformTarget>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <PlatformTarget>x64</PlatformTarget>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="FinEnumerations" Version="1.0.7" />
    </ItemGroup>

</Project>
