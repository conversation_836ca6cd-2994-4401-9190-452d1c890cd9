using System.Collections.Specialized;
using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Domain.Interfaces;

public interface IPricing<in TOption> where TOption : Option
{
    // public DerivativeResult CalculatePrice(TOption option, CalculationMode mode = CalculationMode.ValueOnly);

    public DerivativeResult CalculatePrice(TOption option, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly);

    public DerivativeResult CalculatePrice(TOption option, double[] underlyingSpotPrices,
    CalculationMode mode = CalculationMode.ValueOnly);
}
