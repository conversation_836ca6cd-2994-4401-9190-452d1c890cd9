using FinEnumerations;

namespace DerivativePricing.Domain.Models;

public record AccumulatorOption : Option
{
    public required List<double> Gearing { get; init; }
    public required int InitialAccumulatingDays { get; init; }
    public required List<double> StrikePrices { get; init; }
    public required List<double> BarrierLevels { get; init; }
    public required List<double> Rebates { get; init; }
    public required List<KnockType> KnockTypes { get; init; }
    public required List<ContractType> ContractTypes { get; init; }
    public required Dictionary<string, string> ExpiryObservationFactor { get; init; }
  

}