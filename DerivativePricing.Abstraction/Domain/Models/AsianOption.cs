using FinEnumerations;
using Microsoft.VisualBasic;

namespace DerivativePricing.Domain.Models;

public record AsianOption: VanillaOption
{
    public required AverageType AverageType { get; init; } = AverageType.Arithmetic;
    public required Frequency ObservationFrequency { get; init; }
    public required double FixingPrice { get; init; }
    public required DayCountConvention DayCount { get; init; } = DayCountConvention.TradeDay;
    public required int FixingPeriodLength { get; init; }
    public required double EnhancePrice { get; init; }
    public required double GuaranteedPayoff { get; init; } = 0;
    public required int PathNums { get; init; }
}