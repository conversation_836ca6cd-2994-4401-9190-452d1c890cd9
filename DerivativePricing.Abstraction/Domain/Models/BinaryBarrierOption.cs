using FinEnumerations;

namespace DerivativePricing.Domain.Models;

public record class BinaryBarrierOption : BinaryOption
{
    public required double BarrierLevel { get; init; }
    public required Frequency ObservationFrequency { get; init; }
    public required KnockType KnockType { get; init; }
    public required double Rebate { get; init; }
    public required bool KnockStatus { get; init; }
    
}