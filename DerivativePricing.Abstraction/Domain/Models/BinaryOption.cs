using FinEnumerations;

namespace DerivativePricing.Domain.Models;

public record BinaryOption : VanillaOption
{
    public required BinaryOptionType BinaryType { get; init; }
    public required bool AnnualizedFixedPayment { get; init; }
    public required double FixedPayment { get; init; }
    public required int OriginalDaysLeft { get; init; }
    public required DayCountConvention DayCount { get; init; }
    public required double PriceForSettlement { get; init; }
   
}