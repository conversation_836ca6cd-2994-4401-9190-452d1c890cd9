using FinEnumerations;

namespace DerivativePricing.Domain.Models;

public abstract record class Derivative
{
    public string BusiNo { get; init; } = string.Empty;
    public required double RiskFreeRate { get; init; }
    public required double DividendYield { get; init; }
    public required int DaysToExpiration { get; init; }
    public required int AnnualDenominator { get; init; }
    public required double ParticipationRate { get; init; }
    public required double BasisDiscount { get; init; }
    public required SecType UnderlyingType { get; init; }
}

// public record class SpotPrice(double Value, string Symbol);