namespace DerivativePricing.Domain.Models;

// public readonly struct OptionResult(
//     double Value,
//     List<double> Delta,
//     List<double> Gamma,
//     List<double> Vega,
//     List<double> Volatility,
//     double Theta,
//     double? Rho,
//     double basisDiscount,
//     List<double> UnderlyingSpotPrice)
// {
//     public double Value { get; init; } = Value;
//     public List<double> Delta { get; init; } = Delta;
//     public List<double> Gamma { get; init; } = Gamma;
//     public List<double> Vega { get; init; } = Vega;
//     public double Theta { get; init; } = Theta;
//     public double? Rho { get; init; } = Rho;
//     public List<double> Volatility { get; init; } = Volatility;
//     public double BasisDiscount { get; init; } = basisDiscount;
//     public List<double> UnderlyingSpotPrice { get; init; } = UnderlyingSpotPrice;
// }

public readonly struct DerivativeResult
{
    public string BusiNo { get; init; }
    public double Value { get; init; }
    public double[] Delta { get; init; }
    public double[] Gamma { get; init; }
    public double[] Vega { get; init; }
    public double Theta { get; init; }
    public double Rho { get; init; }
    public double[] Volatility { get; init; }
    public double[] BasisDiscount { get; init; }
    public double[] UnderlyingSpotPrice { get; init; }


    public void AddResult(double gear, ref double value, ref double delta, ref double gamma,
        ref double vega, ref double theta, ref double rho)
    {
        value += this.Value * gear;
        delta += this.Delta[0] * gear;
        gamma += this.Gamma[0] * gear;
        vega += this.Vega[0] * gear;
        theta += this.Theta * gear;
        rho += this.Rho * gear;
    }
}