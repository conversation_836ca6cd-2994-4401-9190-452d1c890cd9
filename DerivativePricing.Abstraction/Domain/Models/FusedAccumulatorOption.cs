using FinEnumerations;

namespace DerivativePricing.Domain.Models;

public record FusedAccumulatorOption : Option
{
    public required double LowerBoundary { get; init; }
    public required double UpperBoundary { get; init; }
    public required double LowerGear { get; init; }
    public required double UpperGear { get; init; }
    public required double MiddleGear { get; init; }
    public required double ExpiryGear { get; init; }
    public required double FixedRangeRebate { get; init; }
    public required double Rebate { get; init; }
    public required AccumulatorPaymentType PaymentType { get; init; }
  

}