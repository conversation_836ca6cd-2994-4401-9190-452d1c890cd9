using FinEnumerations;

namespace DerivativePricing.Domain.Models;

public record Option : Derivative
{
    public required double Volatility { get; init; }
    public required double IntraDayFraction { get; init; }
    public required ContractType ContractType { get; init; }
    public required ExerciseType ExerciseType { get; init; }
    public required PricingMethod PricingMethod { get; init; }
    public required OptionType OptionType { get; init; }
    public double Maturity => DaysToExpiration / (double)AnnualDenominator + IntraDayFraction;
};