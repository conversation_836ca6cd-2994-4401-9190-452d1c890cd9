using FinEnumerations;

namespace DerivativePricing.Domain.Models;

public record SnowballOption : VanillaOption
{
    public required bool EnhanceSnowball { get; init; }
    public required double EnhanceParticipationRate { get; init; }
    public required double[] Coupons { get; init; }
    public required double AdvancePaymentRate { get; init; }
    public required double SwapRate { get; init; }
    public required KnockType KnockType { get; init; }
    public int CouponDateOffset { get; init; } = 0;
    public double DelayPaymentRate { get; init; } = 0.0;
    public required double NoKoKiCoupon { get; init; }
    public required double CalendarDaysLeft { get; init; }
    public required bool KiStatus { get; init; }
    public required SnowballType SnowballType { get; init; }
    public required double[] KoBarriers { get; init; }
    public required int[] KoTradeDaySequence { get; init; }
    public required int[] KoFixedCalendarSequence { get; init; }
    public required double KiPricePercentage { get; init; }
    public int KiObservationStart { get; init; } = 0;
    public required double KiFloorPercentage { get; init; }
    public required bool PayWhenKi { get; init; }
    public int PathNums { get; init; } = 1 << 16;

}