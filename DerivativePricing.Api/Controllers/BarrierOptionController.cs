using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;

namespace DerivativePricing.Api.Controllers;

[ApiController]
[Route("pricing/barrier")]
public class BarrierOptionController(
    IValidator<BarrierOption> validator,
    IPricing<BarrierOption> pricer,
    ILogger<OptionController<BarrierOption>> logger)
    : OptionController<BarrierOption>(validator, pricer, logger);