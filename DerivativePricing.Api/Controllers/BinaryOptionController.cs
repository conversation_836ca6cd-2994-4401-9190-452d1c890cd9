using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;

namespace DerivativePricing.Api.Controllers;

[ApiController]
[Route("pricing/binary")]
public class BinaryOptionController(
    IValidator<BinaryOption> validator,
    IPricing<BinaryOption> pricer,
    ILogger<OptionController<BinaryOption>> logger)
    : OptionController<BinaryOption>(validator, pricer, logger);