using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;

namespace DerivativePricing.Api.Controllers;

/// <summary>
/// option pricing request for options with a single underlying spot price.
/// </summary>
/// <typeparam name="TOption"></typeparam>
public class OptionPricingRequest<TOption>
{
    public required List<TOption> Options { get; init; }
    public required List<double> UnderlyingSpotPrices { get; init; }
}

/// <summary>
/// option pricing request for options with multiple underlying spot prices.
/// </summary>
/// <typeparam name="TOption"></typeparam>
public class OptionPricingRequestMulti<TOption>
{
    public required List<TOption> Options { get; init; }
    public required List<List<double>> UnderlyingSpotPrices { get; init; }
}

public class OptionController<TOption>(
    IValidator<TOption> validator,
    IPricing<TOption> pricer,
    ILogger<OptionController<TOption>> logger) : ControllerBase where TOption : Option
{
    [HttpPost]
    public IActionResult PriceOption([FromBody] OptionPricingRequest<TOption> request,
        [FromQuery] CalculationMode mode)
    {
        var options = request.Options;
        var underlyingSpotPrices = request.UnderlyingSpotPrices;
        List<DerivativeResult> results = [];
        foreach (var (option, spot) in options.Zip(underlyingSpotPrices))
        {
            var validationResult = validator.Validate(option);
            if (validationResult.IsValid)
            {
                var result = pricer.CalculatePrice(option, spot, mode);
                results.Add(result);
            }
            else
            {
                logger.LogError(
                    "Option validation failed: {ValidationResultErrors} for BusiNo {OptionBusiNo}",
                    validationResult.Errors, option.GetType().GetProperty("BusiNo")?.GetValue(option));
            }
        }

        if (results.Count == 0)
        {
            return BadRequest($"No valid {typeof(TOption).Name} options provided.");
        }

        logger.LogInformation("Price {count} {optionType} option(s) succeeded", results.Count, typeof(TOption).Name);
        return Ok(results);
    }
}

public class OptionControllerMulti<TOption>(
    IValidator<TOption> validator,
    IPricing<TOption> pricer,
    ILogger<OptionControllerMulti<TOption>> logger) : ControllerBase where TOption : Option
{
    [HttpPost]
    public IActionResult PriceOption([FromBody] OptionPricingRequestMulti<TOption> request,
        [FromQuery] CalculationMode mode)
    {
        var options = request.Options;
        var underlyingSpotPrices = request.UnderlyingSpotPrices;
        List<DerivativeResult> results = [];
        foreach (var (option, spot) in options.Zip(underlyingSpotPrices))
        {
            var validationResult = validator.Validate(option);
            if (validationResult.IsValid)
            {
                var result = pricer.CalculatePrice(option, spot.ToArray(), mode);
                results.Add(result);
            }
            else
            {
                logger.LogError(
                    "Option validation failed: {ValidationResultErrors} for BusiNo {OptionBusiNo}",
                    validationResult.Errors, option.GetType().GetProperty("BusiNo")?.GetValue(option));
            }
        }

        if (results.Count == 0)
        {
            return BadRequest($"No valid {typeof(TOption).Name} options provided.");
        }

        logger.LogInformation("Price {count} {optionType} option(s) succeeded", results.Count, typeof(TOption).Name);
        return Ok(results);
    }
}