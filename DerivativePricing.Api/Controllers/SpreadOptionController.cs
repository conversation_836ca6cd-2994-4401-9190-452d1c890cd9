using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;

namespace DerivativePricing.Api.Controllers;

// public class SpreadOptionPricingRequest
// {
//     public required List<SpreadOption> Spreads { get; init; }
//     public required List<List<double>> UnderlyingSpotPrices { get; init; }
// }

[ApiController]
[Route("pricing/spread")]
public class SpreadOptionController(
    IValidator<SpreadOption> validator,
    IPricing<SpreadOption> pricer,
    ILogger<OptionControllerMulti<SpreadOption>> logger)
    : OptionControllerMulti<SpreadOption>(validator, pricer, logger);

// [ApiController]
// [Route("pricing/spread")]
// public class SpreadOptionController(
//     IValidator<SpreadOption> validator,
//     SpreadPricer pricer,
//     ILogger<SpreadOptionController> logger) : ControllerBase
// {
//     [HttpPost]
//     public IActionResult PriceSpreadOption([FromBody] SpreadOptionPricingRequest request,
//         [FromQuery] CalculationMode mode)
//     {
//         var spreads = request.Spreads;
//         var underlyingSpotPrices = request.UnderlyingSpotPrices;
//         List<DerivativeResult> results = [];
//         foreach (var (spread, spot) in spreads.Zip(underlyingSpotPrices))
//         {
//             
//             var validationResult = validator.Validate(spread);
//             if (validationResult.IsValid)
//             {
//                 var result = pricer.CalculatePrice(spread, spot.ToArray(), mode);
//                 results.Add(result);
//             }
//             else
//             {
//                 logger.LogError(
//                     "Spread option validation failed: {ValidationResultErrors} for BusiNo {SpreadBusiNo}",
//                     validationResult.Errors, spread.BusiNo);
//             }
//         }
//
//         if (results.Count == 0)
//         {
//             return BadRequest("No valid SpreadOption provided.");
//         }
//         logger.LogInformation("Price {count} spread option(s) succeeded", results.Count);
//         return Ok(results);
//     }
// }