using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;

namespace DerivativePricing.Api.Controllers;

[ApiController]
[Route("pricing/vanilla")]
public class VanillaOptionController(
    IValidator<VanillaOption> validator,
    IPricing<VanillaOption> pricer,
    ILogger<OptionController<VanillaOption>> logger)
    : OptionController<VanillaOption>(validator, pricer, logger);