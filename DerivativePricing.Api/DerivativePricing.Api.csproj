<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentValidation" Version="12.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.16"/>
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.Grafana.Loki" Version="8.3.1" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\DerivativePricing.Abstraction\DerivativePricing.Abstraction.csproj" />
      <ProjectReference Include="..\DerivativePricing.Core\DerivativePricing.Core.csproj" />
    </ItemGroup>

</Project>
