using System.Text.Json.Serialization;
using DerivativePricing.Api.Validators;
using DerivativePricing.Core.Internals;
using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FluentValidation;
using Serilog;
using Serilog.Sinks.Grafana.Loki;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var settingPath = @"D:\ConfigFiles\C#\OTCManagementSystem\appsettings.json";
var uri = (string)Utils.ReadSetting(settingPath, "Loki", "Uri")! ?? "http://***********:3100";

builder.Host.UseSerilog((context, configuration) =>
{
    configuration
        .ReadFrom.Configuration(context.Configuration)
        .WriteTo.GrafanaLoki(uri, [
            new LokiLabel { Key = "app", Value = "DerivativePricing" },
            new LokiLabel { Key = "environment", Value = builder.Environment.EnvironmentName }
        ]);
});

builder.Services.AddControllers();
// Add double.NaN support for JSON serialization
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.NumberHandling = JsonNumberHandling.AllowNamedFloatingPointLiterals;
        options.JsonSerializerOptions.PropertyNamingPolicy = null;
        options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
    });

// add FluentValidation
builder.Services.AddScoped<IValidator<VanillaOption>, VanillaOptionValidator>();
builder.Services.AddScoped<IValidator<BarrierOption>, BarrierOptionValidator>();
builder.Services.AddScoped<IValidator<SpreadOption>, SpreadOptionValidator>();
builder.Services.AddScoped<IValidator<BinaryOption>, BinaryOptionValidator>();

// add options
builder.Services.AddScoped<IPricing<VanillaOption>, VanillaPricer>();
builder.Services.AddScoped<IPricing<BarrierOption>, BarrierPricer>();
builder.Services.AddScoped<IPricing<SpreadOption>, SpreadPricer>();



var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.UseRouting();
app.MapControllers();

// Add Serilog request logging
app.UseSerilogRequestLogging();

app.Run();