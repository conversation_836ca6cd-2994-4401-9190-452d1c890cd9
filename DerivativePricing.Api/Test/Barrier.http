POST http://localhost:5555/pricing/barrier?mode=ValueOnly
Content-Type: application/json
Accept: application/json

{
  "Options": [
    {
      "BusiNo": "TEST001",
      "StrikePrice": 85,
      "BarrierLevel" : 85,
      "Volatility" : 0.2,
      "RiskFreeRate" : 0.03,
      "DividendYield" : 0.0,
      "BasisDiscount" : -0.0,
      "DaysToExpiration" : 4,
      "IntraDayFraction" : 0,
      "ContractType" : "Call",
      "PricingMethod" : "Analytic",
      "UnderlyingType" : "CommodityFutures",
      "ExerciseType" : "EuropeanExercise",
      "OptionType" : "Barrier",
      "AnnualDenominator" : 243,
      "ParticipationRate" : 1.0,
      "KnockType" : "DownIn",
      "KnockStatus" : false,
      "Rebate" : 0.0,
      "ObservationFrequency" : "Daily",
      "ObservationSequence": []
    }
  ],
  "UnderlyingSpotPrices": [85]
}


