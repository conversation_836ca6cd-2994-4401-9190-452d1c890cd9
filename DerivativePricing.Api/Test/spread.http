POST http://localhost:5555/pricing/spread?mode=AllGreeks
Content-Type: application/json
Accept: application/json

{
  "Options": [
    {
      "StrikePrice": 0,
      "Volatility": 0.15,
      "Volatility2": 0.15,
      "Volume": 1,
      "Volume2": 1,
      "Rho": 0.85,
      "RiskFreeRate": 0.03,
      "DividendYield": 0.0,
      "BasisDiscount": 0.0,
      "BasisDiscount2": 0.0,
      "DaysToExpiration": 24,
      "IntraDayFraction": 0,
      "ContractType": "Call",
      "PricingMethod": "Analytic",
      "UnderlyingType": "CommodityFutures",
      "UnderlyingType2": "CommodityFutures",
      "ExerciseType": "EuropeanExercise",
      "OptionType": "SpreadOption",
      "AnnualDenominator": 243,
      "ParticipationRate": 1,
      "BusiNo": "TEST001"
    }
  ],
  "UnderlyingSpotPrices": [[100,100]]
}