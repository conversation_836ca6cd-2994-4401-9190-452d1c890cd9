using DerivativePricing.Domain.Models;
using FinEnumerations;
using FluentValidation;


namespace DerivativePricing.Api.Validators;

public class BarrierOptionValidator: AbstractValidator<BarrierOption>
{
    public BarrierOptionValidator()
    {
        RuleFor(option => option.StrikePrice).GreaterThan(0).WithMessage("Strike price must be greater than 0.");
        RuleFor(option => option.IntraDayFraction).InclusiveBetween(0, 1).WithMessage("Intra-day fraction must be between 0 and 1.");
        RuleFor(option => option.ContractType)
            .Must(x => x is ContractType.Call or ContractType.Put)
            .WithMessage("Contract type must be either Call or Put.");
        RuleFor(option => option.ExerciseType)
            .Must(x => x is ExerciseType.EuropeanExercise)
            .WithMessage("Exercise type must be European Exercise for Barrier Option.");
        RuleFor(option=> option.KnockType)
            .Must(x => x is KnockType.DownIn or KnockType.DownOut or KnockType.UpOut or KnockType.UpIn)
            .WithMessage("Knock type must be DownIn, DownOut, UpOut or UpIn.");
        RuleFor(option => option.BarrierLevel).GreaterThan(0).WithMessage("Barrier level must be greater than 0.");
   
    }
}