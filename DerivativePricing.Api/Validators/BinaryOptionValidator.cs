using DerivativePricing.Domain.Models;
using FinEnumerations;
using FluentValidation;

namespace DerivativePricing.Api.Validators;

public class BinaryOptionValidator : AbstractValidator<BinaryOption>
{
    public BinaryOptionValidator()
    {
        RuleFor(option => option.StrikePrice).GreaterThan(0).WithMessage("Strike price must be greater than 0.");
        RuleFor(option => option.IntraDayFraction).InclusiveBetween(0, 1)
            .WithMessage("Intra-day fraction must be between 0 and 1.");
        RuleFor(option => option.ContractType)
            .Must(x => x is ContractType.Call or ContractType.Put)
            .WithMessage("Contract type must be either Call or Put.");
        RuleFor(option => option.ExerciseType)
            .Equal(ExerciseType.EuropeanExercise)
            .WithMessage("Exercise type must be European Exercise for Binary.");
        RuleFor(option => option.PricingMethod)
            .Equal(PricingMethod.Analytic)
            .WithMessage("Pricing method must be Analytic.");
        RuleFor(option => option.OptionType)
            .Equal(OptionType.Binary)
            .WithMessage("Option type must be Binary.");
        RuleFor(option => option.OriginalDaysLeft)
            .GreaterThanOrEqualTo(option => option.DaysToExpiration)
            .WithMessage("Original days left must be greater than or equal to days to expiration.");
    }
}