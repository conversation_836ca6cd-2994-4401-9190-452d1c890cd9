using DerivativePricing.Domain.Models;
using FinEnumerations;
using FluentValidation;

namespace DerivativePricing.Api.Validators;

public class SpreadOptionValidator : AbstractValidator<SpreadOption>
{
    public SpreadOptionValidator()
    {
        RuleFor(option => option.IntraDayFraction).InclusiveBetween(0, 1)
            .WithMessage("Intra-day fraction must be between 0 and 1.");
        RuleFor(option => option.Volume).GreaterThanOrEqualTo(1.0).WithMessage("Volume must be greater than 1.0.");
        RuleFor(option => option.Volume2).GreaterThanOrEqualTo(1.0).WithMessage("Volume must be greater than 1.0.");
        RuleFor(option => option.ContractType)
            .Must(x => x is ContractType.Call or ContractType.Put)
            .WithMessage("Contract type must be either Call or Put.");
        RuleFor(option => option.ExerciseType)
            .Equal(ExerciseType.EuropeanExercise);
        RuleFor(option => option.PricingMethod)
            .Equal(PricingMethod.Analytic)
            .WithMessage("Pricing method must be Analytic.");
        RuleFor(option => option.OptionType)
            .Equal(OptionType.SpreadOption)
            .WithMessage("Option type must be SpreadOption.");
        RuleFor(option => option.Rho).InclusiveBetween(-1, 1)
            .WithMessage("Rho must be between -1 and 1.");
    }
}