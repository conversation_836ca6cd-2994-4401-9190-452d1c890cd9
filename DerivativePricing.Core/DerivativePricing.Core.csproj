<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <PlatformTarget>x64</PlatformTarget>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <PlatformTarget>x64</PlatformTarget>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\DerivativePricing.Abstraction\DerivativePricing.Abstraction.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="FinEnumerations" Version="1.0.7" />
      <PackageReference Include="ILGPU" Version="1.5.2" />
      <PackageReference Include="ILGPU.Algorithms" Version="1.5.2" />
      <PackageReference Include="MathNet.Numerics" Version="5.0.0" />
      <PackageReference Include="QLNet" Version="1.13.1" />
    </ItemGroup>

</Project>
