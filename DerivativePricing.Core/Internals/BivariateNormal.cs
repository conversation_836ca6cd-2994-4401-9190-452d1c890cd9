using System.Numerics;
using MathNet.Numerics.Distributions;
using MathNet.Numerics.LinearAlgebra;

namespace DerivativePricing.Core.Internals;

public static partial class Utils
{
    private static readonly double[,] XX = new double[10, 3]
    {
        { -0.932469514203152, -0.981560634246719, -0.993128599185095 },
        { -0.661209386466265, -0.904117256370475, -0.963971927277914 },
        { -0.238619186083197, -0.769902674194305, -0.912234428251326 },
        { 0, -0.587317954286617, -0.839116971822219 },
        { 0, -0.36783149899818, -0.746331906460151 },
        { 0, -0.125233408511469, -0.636053680726515 },
        { 0, 0, -0.510867001950827 },
        { 0, 0, -0.37370608871542 },
        { 0, 0, -0.227785851141645 },
        { 0, 0, -0.0765265211334973 }
    };

    private static readonly double[,] W = new double[10, 3]
    {
        { 0.17132449237917, 0.0471753363865118, 0.0176140071391521 },
        { 0.360761573048138, 0.106939325995318, 0.0406014298003869 },
        { 0.46791393457269, 0.160078328543346, 0.0626720483341091 },
        { 0, 0.203167426723066, 0.0832767415767048 },
        { 0, 0.233492536538355, 0.10193011981724 },
        { 0, 0.249147045813403, 0.118194531961518 },
        { 0, 0, 0.131688638449177 },
        { 0, 0, 0.142096109318382 },
        { 0, 0, 0.149172986472604 },
        { 0, 0, 0.152753387130726 }
    };

    public static double BivariateCDF(double X, double Y, double rho)
    {
        int NG, LG;
        var absRho = Math.Abs(rho);
        switch (absRho)
        {
            case < 0.3:
                NG = 0;
                LG = 3;
                break;
            case < 0.75:
                NG = 1;
                LG = 6;
                break;
            default:
                NG = 2;
                LG = 10;
                break;
        }

        double h = -X, k = -Y, hk = h * k, BVN = 0.0;

        if (absRho < 0.925)
        {
            if (absRho > 0)
            {
                double hs = (h * h + k * k) / 2.0;
                double asr = ArcSin(rho);
                for (int i = 0; i < LG; i++)
                {
                    for (int iss = -1; iss <= 1; iss += 2)
                    {
                        double sn = Math.Sin(asr * (iss * XX[i, NG] + 1) / 2.0);
                        BVN += W[i, NG] * Math.Exp((sn * hk - hs) / (1 - sn * sn));
                    }
                }

                BVN = BVN * asr / (4.0 * Math.PI);
            }

            BVN += Normal.CDF(0, 1, -h) * Normal.CDF(0, 1, -k);
        }
        else
        {
            if (rho < 0)
            {
                k = -k;
                hk = -hk;
            }

            if (absRho < 1)
            {
                var Ass = (1 - rho) * (1 + rho);
                var A = Math.Sqrt(Ass);
                var bs = (h - k) * (h - k);
                var c = (4 - hk) / 8.0;
                var d = (12 - hk) / 16.0;
                var asr = -(bs / Ass + hk) / 2.0;
                if (asr > -100)
                    BVN = A * Math.Exp(asr) * (1 - c * (bs - Ass) * (1 - d * bs / 5.0) / 3.0 + c * d * Ass * Ass / 5.0);
                if (-hk < 100)
                {
                    var b = Math.Sqrt(bs);
                    BVN -= Math.Exp(-hk / 2.0) * Math.Sqrt(2 * Math.PI) * Normal.CDF(0, 1, -b / A) * b *
                           (1 - c * bs * (1 - d * bs / 5.0) / 3.0);
                }

                A = A / 2.0;
                for (var i = 0; i < LG; i++)
                {
                    for (var iss = -1; iss <= 1; iss += 2)
                    {
                        var xs = Math.Pow(A * (iss * XX[i, NG] + 1), 2);
                        var rs = Math.Sqrt(1 - xs);
                        asr = -(bs / xs + hk) / 2.0;
                        if (asr > -100)
                        {
                            BVN += A * W[i, NG] * Math.Exp(asr) *
                                   (Math.Exp(-hk * (1 - rs) / (2 * (1 + rs))) / rs -
                                    (1 + c * xs * (1 + d * xs)));
                        }
                    }
                }

                BVN = -BVN / (2 * Math.PI);
            }

            if (rho > 0)
                BVN += Normal.CDF(0, 1, -Math.Max(h, k));
            else
            {
                BVN = -BVN;
                if (k > h)
                    BVN += Normal.CDF(0, 1, k) - Normal.CDF(0, 1, h);
            }
        }

        return BVN;
    }

    public static double BivariateCDF_SIMD(double X, double Y, double rho)
    {
        int NG, LG;
        var absRho = Math.Abs(rho);
        switch (absRho)
        {
            case < 0.3:
                NG = 0;
                LG = 3;
                break;
            case < 0.75:
                NG = 1;
                LG = 6;
                break;
            default:
                NG = 2;
                LG = 10;
                break;
        }

        double h = -X, k = -Y, hk = h * k, BVN = 0.0;

        if (absRho < 0.925)
        {
            if (absRho > 0)
            {
                double hs = (h * h + k * k) / 2.0;
                double asr = Math.Asin(rho);

                // Prepare MathNet vectors for XX and W
                var xx = MathNet.Numerics.LinearAlgebra.Vector<double>.Build.DenseOfArray(GetColumn(XX, NG, LG));
                var w = MathNet.Numerics.LinearAlgebra.Vector<double>.Build.DenseOfArray(GetColumn(W, NG, LG));
                var one = MathNet.Numerics.LinearAlgebra.Vector<double>.Build.Dense(LG, 1.0);

                // iss = 1
                var sn1 = (xx + one).Multiply(asr / 2.0).PointwiseSin();
                var den1 = one - sn1.PointwisePower(2.0);
                var exp1 = ((sn1 * hk - hs).PointwiseDivide(den1)).PointwiseExp();

                // iss = -1
                var sn2 = (xx.Negate() + one).Multiply(asr / 2.0).PointwiseSin();
                var den2 = one - sn2.PointwisePower(2.0);
                var exp2 = ((sn2 * hk - hs).PointwiseDivide(den2)).PointwiseExp();

                BVN += w.DotProduct(exp1 + exp2);

                BVN = BVN * asr / (4.0 * Math.PI);
            }

            BVN += Normal.CDF(0, 1, -h) * Normal.CDF(0, 1, -k);
        }

        // For brevity, the rest of the function (absRho >= 0.925) is unchanged.
        return BVN;
    }

// Helper to get a column from a 2D array as a double[]
    private static double[] GetColumn(double[,] array, int col, int length)
    {
        var result = new double[length];
        for (int i = 0; i < length; i++)
            result[i] = array[i, col];
        return result;
    }
}