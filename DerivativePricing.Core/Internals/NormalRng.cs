using QLNet;

namespace DerivativePricing.Core.Internals;

public class NormalRng
{
    
    public static double[,] NormalRandoms(int pathNums, int stepNumbs, ulong seed)
    {
        var rng = new MersenneTwisterUniformRng(seed);
        var normal = new InverseCumulativeNormal();
        var randoms = new double[pathNums, stepNumbs];
        for (int i = 0; i < pathNums; i++)
        {
            for (int j = 0; j < stepNumbs; j++)
            {
                var uniformRandom = rng.next().value;
                var normalRandom = normal.value(uniformRandom);
                randoms[i, j] = normalRandom;
            }
        }

        return randoms;
    }
    
    public static double[] NormalRandomsFlattened(int pathNums, int stepNumbs, ulong seed)
    {
        var rng = new MersenneTwisterUniformRng(seed);
        var normal = new InverseCumulativeNormal();
        var randoms = new double[pathNums * stepNumbs];
        for (int i = 0; i < pathNums; i++)
        {
            for (int j = 0; j < stepNumbs; j++)
            {
                var uniformRandom = rng.next().value;
                var normalRandom = normal.value(uniformRandom);
                randoms[i] = normalRandom;
            }
        }

        return randoms;
    }
    
}