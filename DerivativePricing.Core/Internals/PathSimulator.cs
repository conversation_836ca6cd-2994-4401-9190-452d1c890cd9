using ILGPU;
using ILGPU.Runtime;
using ILGPU.Runtime.CPU;
using ILGPU.Runtime.Cuda;
using ILGPU.Algorithms;
using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Core.Internals;

public class PathSimulator
{
    private static string Arch;
    private static ParallelOptions parallelOptions;
    public static int FixPathNums { get; private set; }
    private static int FixStepNums { get; set; }
    private static Context GpuContext;
    private static Accelerator GpuAccelerator;
    private static MemoryBuffer1D<double, Stride1D.Dense> RandomOnDevice1D;
    
    
    public static double[] PreComputedRandoms { get; private set; } = [];

    public static void Init(int pathNums, int stepNums, string arch, ulong seed = 159357)
    {
        FixPathNums = pathNums;
        FixStepNums = stepNums;
        PreComputedRandoms = QuasiRng.SobolQuasiRandomsFlattened(pathNums, stepNums, seed);
        
        Arch = arch.ToUpper();
        if (Arch is "DEBUG" or "GPU")
        {
            try
            {
                if (Arch == "GPU")
                {
                    GpuContext = Context.Create(builder =>
                        builder.Math(MathMode.Fast32BitOnly).Cuda().EnableAlgorithms());
                    var cudaDevice = GpuContext.GetCudaDevices();
                    GpuAccelerator = cudaDevice[0].CreateCudaAccelerator(GpuContext);
                }
                else
                {
                    Console.WriteLine("Debug mode");
                    GpuContext = Context.Create(builder => builder.CPU());
                    var cpuDevice = GpuContext.GetCPUDevices();
                    GpuAccelerator = cpuDevice[0].CreateCPUAccelerator(GpuContext);
                }
                RandomOnDevice1D = GpuAccelerator.Allocate1D<double>(pathNums * stepNums);
                RandomOnDevice1D.CopyFromCPU(PreComputedRandoms);
                
            }
            catch (Exception e)
            {
                Console.WriteLine("failed to initialize gpu context");
                throw;
            }
        }
        if (Arch == "CPU")
        {
            var cpuCount = Environment.ProcessorCount;
            var realCount = cpuCount switch
            {
                8 => 8,
                16 => 16,
                64 => 16,
                80 => 16,
                _ => 4
            };
            parallelOptions = new ParallelOptions { MaxDegreeOfParallelism = realCount };
        }
        else
        {
            throw new ArgumentException("Unsupported architecture. Supported architectures are: DEBUG, GPU, CPU.");
        }
    }

    public static void ReInitialize(int pathNums, int stepNums, string arch, ulong seed = 159357)
    {
        PreComputedRandoms = [];
        Init(pathNums, stepNums, arch, seed);
    }


    public static double[] Simulate(double s0, double mu, double sigma, double dt, int pathNums,
        int stepNums)
    {
        if (pathNums * stepNums > PreComputedRandoms.Length)
        {
            throw new ArgumentException(
                $"Requested {pathNums * stepNums} random numbers, but only {PreComputedRandoms.Length} are precomputed.");
        }
        var length = pathNums * (stepNums + 1);
        

        var randoms = PreComputedRandoms; // day zero price
        var simulatedPaths = GC.AllocateUninitializedArray<double>(length);
        var drift = (mu - 0.5 * sigma * sigma) * dt;
        var expDrift = Math.Exp(drift);
        var sqrtDt = Math.Sqrt(dt);
        var constDiffusion = sigma * sqrtDt;
        Parallel.For(0, pathNums, parallelOptions, i =>
        {
            var offset = i * (stepNums + 1);
            var randomOffset = i * FixStepNums; // in order to comply with python codes
            simulatedPaths[offset] = s0; // day zero price
            for (int j = 0; j < stepNums; j++)
            {
                simulatedPaths[offset + j + 1] =
                    simulatedPaths[offset + j] * expDrift * Math.Exp(constDiffusion * randoms[randomOffset + j]);
            }
        });

        return simulatedPaths;
    }


    public static double[] SimulateCandidate1(double s0, double mu, double sigma, double dt, int pathNums,
        int stepNums)
    {
        var length = pathNums * (stepNums + 1);
        if (length > PreComputedRandoms.Length)
            throw new ArgumentException(
                $"Requested {length} random numbers, but only {PreComputedRandoms.Length} are precomputed.");

        var randoms = PreComputedRandoms;
        var simulatedPathsArr = GC.AllocateUninitializedArray<double>(length);

        var drift = (mu - 0.5 * sigma * sigma) * dt;
        var expDrift = Math.Exp(drift);
        var sqrtDt = Math.Sqrt(dt);
        var constDiffusion = sigma * sqrtDt;

        Parallel.For(0, pathNums, parallelOptions, i =>
        {
            int offset = i * (stepNums + 1);
            var randomOffset = i * FixStepNums;
            var pathSpan = simulatedPathsArr.AsSpan(offset, stepNums + 1);
            pathSpan[0] = s0;
            for (int j = 0; j < stepNums; j++)
            {
                // int idx = offset + j;
                pathSpan[j + 1] = pathSpan[j] * expDrift * Math.Exp(constDiffusion * randoms[randomOffset + j]);
            }
        });

        return simulatedPathsArr;
    }

   

    /// <summary>
    /// 
    /// </summary>
    /// <param name="vanilla"></param>
    /// <returns>mu, steps, dt, sigma, parallel</returns>
    public static (double, int, double, double, ParallelOptions) McFactor(VanillaOption vanilla)
    {
        var mu = vanilla.UnderlyingType switch
        {
            SecType.Stock => vanilla.RiskFreeRate - vanilla.DividendYield,
            SecType.CommoditySpot => -vanilla.BasisDiscount,
            SecType.CommodityIndex => -vanilla.BasisDiscount,
            SecType.EquityIndex => -vanilla.BasisDiscount,
            SecType.CommodityFutures => 0.0,
            _ => vanilla.RiskFreeRate
        };
        var steps = vanilla.DaysToExpiration;
        var dt = 1.0 / vanilla.AnnualDenominator;
        var sigma = vanilla.Volatility;
        return (mu, steps, dt, sigma, parallelOptions);
    }
    
    public static void SimulateGpu(double s0, double mu, double sigma, double dt, int pathNums,
        int stepNums, ref MemoryBuffer1D<double, Stride1D.Dense> resultBuffer)
    {
        
       
        var drift = (mu - 0.5 * sigma * sigma) * dt;
        var expDrift = Math.Exp(drift);
        var sqrtDt = Math.Sqrt(dt);
        var constDiffusion = sigma * sqrtDt;
        var randomsBuffer = RandomOnDevice1D;
        var accelerator = GpuAccelerator;
        var loadedKernel =
            accelerator
                .LoadAutoGroupedStreamKernel<Index1D, ArrayView<double>, ArrayView<double>, double, double, double, int>(
                    AutoPathKernel);

        // AutoPathKernelTest3(pathNums, randomsBuffer.View, resultBuffer.View, s0, expDrift, constDiffusion, steps);
        loadedKernel(pathNums, randomsBuffer.View, resultBuffer.View, s0, expDrift, constDiffusion, stepNums);
        accelerator.Synchronize();
    }
    
    public static void AutoPathKernel(
        Index1D i,
        ArrayView<double> randoms,
        ArrayView<double> pathArr,
        double s0,
        double expDrift,
        double constDiffusion,
        int steps)
    {
        var pathLength = steps + 1;
        int pathIdx = i;

        if (pathIdx >= pathArr.Length / pathLength) return;

        // Initialize first step
        var baseIdx = pathIdx * pathLength;
        pathArr[baseIdx] = s0;

        // Simulate path
        for (var j = 0; j < steps; j++)
        {
            var randVal = randoms[pathIdx * steps + j];
            var expDiff = XMath.Exp(constDiffusion * randVal);
            pathArr[baseIdx + j + 1] = pathArr[baseIdx + j] * expDrift * expDiff;
        }
    }
}