using MathNet.Numerics.Distributions;
using QLNet;

namespace DerivativePricing.Core.Internals;

public class QuasiRng
{


    public static double[,] SobolQuasiRandoms(int pathNums, int stepNums, ulong seed = 159357)
    {
        var sobolGen = new SobolRsg(stepNums, seed);
        var logPathNum = (int)Math.Ceiling(Math.Log(pathNums, 2));
        var realPathNums = 1 << logPathNum;
        var randoms = new double[realPathNums, stepNums];
        sobolGen.nextSequence();
        for (int i = 0; i < pathNums; i++)
        {
            var sobolSeq = sobolGen.nextSequence();
            for (int j = 0; j < stepNums; j++)
            {
                randoms[i, j] = Normal.InvCDF(0, 1, sobolSeq.value[j]);
            }
        }

        return randoms;
    }
    
    public static double[] SobolQuasiRandomsFlattened(int pathNums, int stepNums, ulong seed = 159357)
    {
        var sobolGen = new SobolRsg(stepNums, seed);
        var logPathNum = (int)Math.Ceiling(Math.Log(pathNums, 2));
        var realPathNums = 1 << logPathNum;
        var randoms = new double[realPathNums * stepNums];
        sobolGen.nextSequence();
        for (int i = 0; i < pathNums; i++)
        {
            var sobolSeq = sobolGen.nextSequence();
            for (int j = 0; j < stepNums; j++)
            {
                randoms[i * stepNums + j] = Normal.InvCDF(0, 1, sobolSeq.value[j]);
            }
        }

        return randoms;
    }

    // private double[,] SobolQuasiRandomsParallel()
    // {
    //     var sobolGen = new SobolRsg(_stepNums, _seed);
    //     var logPathNum = (int)Math.Round(Math.Log(_quasiPathNums, 2), 0);
    //     var pathNums = 1 << logPathNum;
    //     var randoms = new double[pathNums, _stepNums];
    //     Parallel.For(0, pathNums, i =>
    //     {
    //         var sobolSeq = sobolGen.nextSequence();
    //         for (int j = 0; j < _stepNums; j++)
    //         {
    //             randoms[i, j] = Normal.InvCDF(0, 1, sobolSeq.value[j]);
    //         }
    //     });
    //
    //
    //     return randoms;
    // }
    
}