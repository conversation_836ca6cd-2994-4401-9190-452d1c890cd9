using System.Runtime.CompilerServices;
using System.Text.Json;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using MathNet.Numerics;

namespace DerivativePricing.Core.Internals;

public static partial class Utils
{
    public static double CarryCost(VanillaOption option)
    {
        return option.UnderlyingType switch
        {
            SecType.Stock => option.RiskFreeRate - option.DividendYield,
            SecType.CommoditySpot => -option.BasisDiscount,
            SecType.CommodityIndex => -option.BasisDiscount,
            SecType.EquityIndex => -option.BasisDiscount,
            SecType.CommodityFutures => 0,
            SecType.CommodityIndexFutures => 0,
            _ => throw new ArgumentOutOfRangeException($"")
        };
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static (double, double) FiniteDiffDeltaGamma<TOption>(TOption option, double optionValue,
        double underlyingSpotPrice, Func<TOption, double, DerivativeResult> calculatePrice, double priceBump = 0.001)
        where TOption : VanillaOption

    {
        var realBump = underlyingSpotPrice * priceBump;
        var upValue = calculatePrice(option, underlyingSpotPrice + realBump).Value;
        var downValue = calculatePrice(option, underlyingSpotPrice - realBump).Value;
        var delta = (upValue - downValue) / (2 * realBump);
        var gamma = (upValue - 2 * optionValue + downValue) / realBump / realBump;
        return (delta, gamma);
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static double FiniteDiffVega<TOption>(TOption option, double underlyingSpotPrice,
        Func<TOption, double, DerivativeResult> calculatePrice, double volBump = 0.01)
        where TOption : VanillaOption

    {
        var upOption = option with { Volatility = option.Volatility + volBump };
        var downOption = option with { Volatility = option.Volatility - volBump };
        var upValue = calculatePrice(upOption, underlyingSpotPrice).Value;
        var downValue = calculatePrice(downOption, underlyingSpotPrice).Value;
        var vega = (upValue - downValue) / (2 * volBump);
        return vega * 0.01;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static double FiniteDiffTheta<TOption>(TOption option, double underlyingSpotPrice,
        Func<TOption, double, DerivativeResult> calculatePrice, bool byPercent = true)
        where TOption : VanillaOption
    {
        var realBump = byPercent ? option.Maturity * 0.001 : 1.0 / 243 / 6 / 6;
        if (option.Maturity <= realBump)
            return 0.0;
        var nextOption = option with { IntraDayFraction = option.IntraDayFraction - realBump };
        var prevOption = option with { IntraDayFraction = option.IntraDayFraction + realBump };
        var nextValue = calculatePrice(nextOption, underlyingSpotPrice).Value;
        var prevValue = calculatePrice(prevOption, underlyingSpotPrice).Value;
        var theta = (nextValue - prevValue) / (2 * realBump);
        return theta / option.AnnualDenominator;
    }

    public static double FiniteDiffThetaDaily<TOption>(TOption option, double optionValue,
        double underlyingSpotPrice, Func<TOption, double, DerivativeResult> calculatePrice)
        where TOption : VanillaOption
    {
        var remainingDays = option.DaysToExpiration - 1;
        if (remainingDays <= 0)
            return 0.0;
        var nextOption = option with { DaysToExpiration = remainingDays };
        var nextValue = calculatePrice(nextOption, underlyingSpotPrice).Value;
        var theta = (nextValue - optionValue) / option.AnnualDenominator;
        return theta;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static double FiniteDiffRho<TOption>(TOption option, double underlyingSpotPrice,
        Func<TOption, double, DerivativeResult> calculatePrice, double rhoBump = 0.01)
        where TOption : VanillaOption

    {
        var upOption = option with { RiskFreeRate = option.RiskFreeRate + rhoBump };
        var downOption = option with { RiskFreeRate = option.RiskFreeRate - rhoBump };
        var upValue = calculatePrice(upOption, underlyingSpotPrice).Value;
        var downValue = calculatePrice(downOption, underlyingSpotPrice).Value;
        var rho = (upValue - downValue) / (2 * rhoBump);
        return rho * 0.01;
    }

    private static double ArcSin(double x)
    {
        if (Math.Abs(Math.Abs(x) - 1.0) < 1e-6)
            return Math.Sign(x) * Math.PI / 2;
        return Math.Atan(x / Math.Sqrt(1 - x * x));
    }

    public static double NormalCDF(double x)
    {
        return SpecialFunctions.Erf(-x / 1.4142135623730951) / 2.0;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int FirstOccurrenceEquals(ReadOnlySpan<double> target, ReadOnlySpan<double> compareBase,
        ReadOnlySpan<int> observationSeq, double tolerance = 1e-9)
    {
        var length = compareBase.Length;

        for (int i = 0; i < length; i++)
        {
            var obsIndex = observationSeq[i];
            var diff = target[obsIndex] - compareBase[i];
            if (Math.Abs(diff) <= tolerance)
                return i;
        }

        return -1;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int FirstOccurrenceGreaterThan(ReadOnlySpan<double> target, ReadOnlySpan<double> compareBase,
        ReadOnlySpan<int> observationSeq)
    {
        var length = compareBase.Length;

        for (int i = 0; i < length; i++)
        {
            var obsIndex = observationSeq[i];
            if (target[obsIndex] > compareBase[i])
                return i;
        }

        return -1;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int FirstOccurrenceGreaterThan(ReadOnlySpan<double> target, ReadOnlySpan<double> compareBase)
    {
        var length = compareBase.Length;

        for (int i = 0; i < length; i++)
        {
            if (target[i] > compareBase[i])
                return i;
        }

        return -1;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int FirstOccurrenceLessThan(ReadOnlySpan<double> target, ReadOnlySpan<double> compareBase,
        ReadOnlySpan<int> observationSeq)
    {
        var length = compareBase.Length;

        for (int i = 0; i < length; i++)
        {
            var obsIndex = observationSeq[i];
            if (target[obsIndex] < compareBase[i])
                return i;
        }

        return -1;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int FirstOccurrenceLessThan(ReadOnlySpan<double> target, ReadOnlySpan<double> compareBase)
    {
        var length = compareBase.Length;

        for (int i = 0; i < length; i++)
        {
            if (target[i] < compareBase[i])
                return i;
        }

        return -1;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int FirstOccurrenceLessThanOrEqual(ReadOnlySpan<double> target, ReadOnlySpan<double> compareBase,
        ReadOnlySpan<int> observationSeq)
    {
        var length = compareBase.Length;

        for (int i = 0; i < length; i++)
        {
            var obsIndex = observationSeq[i];
            if (target[obsIndex] <= compareBase[i])
                return i;
        }

        return -1;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int FirstOccurrenceGreaterThanOrEqual(ReadOnlySpan<double> target, ReadOnlySpan<double> compareBase,
        ReadOnlySpan<int> observationSeq)
    {
        var length = compareBase.Length;

        for (int i = 0; i < length; i++)
        {
            var obsIndex = observationSeq[i];
            if (target[obsIndex] >= compareBase[i])
                return i;
        }

        return -1;
    }

    public static class OptimizedSearch
    {
        // Main optimized version - uses Span<T> for all parameters
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static int FirstOccurrence(ReadOnlySpan<double> target, ReadOnlySpan<double> compareBase,
            ReadOnlySpan<int> observationSeq, Func<double, double, bool> condition)
        {
            var length = compareBase.Length;

            // Bounds checking - let the runtime optimize these away in release builds
            if (length > observationSeq.Length)
                throw new ArgumentException("observationSeq must be at least as long as compareBase");

            for (int i = 0; i < length; i++)
            {
                var obsIndex = observationSeq[i];
                if ((uint)obsIndex >= (uint)target.Length)
                    throw new IndexOutOfRangeException($"observationSeq[{i}] = {obsIndex} is out of bounds");

                if (condition(target[obsIndex], compareBase[i]))
                    return i;
            }

            return -1; // More conventional than Int32.MaxValue
        }
    }

    public static object? ReadSetting(string settingPath, string jsonRoot, string key)
    {
        var lines = File.ReadAllLines(settingPath)
            .Where(line => !line.TrimStart().StartsWith("//"));
        var json = string.Join(Environment.NewLine, lines);
        using var doc = JsonDocument.Parse(json);
        var root = doc.RootElement;
        var jsonElement = root.GetProperty(jsonRoot);
        var value = jsonElement.GetProperty(key);

        return value.ValueKind switch
        {
            JsonValueKind.String => value.GetString(),
            JsonValueKind.Number when value.TryGetInt32(out var intValue) => intValue,
            JsonValueKind.Number => value.GetDouble(),
            _ => value.GetRawText()
        };
    }

}
        
