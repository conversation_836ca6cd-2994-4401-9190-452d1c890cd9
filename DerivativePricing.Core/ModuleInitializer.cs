using System.Runtime.CompilerServices;
using System.Text.Json;
using DerivativePricing.Core.Internals;

namespace DerivativePricing.Core;

public static class ModuleInit
{
    [ModuleInitializer]
    public static void Initialize()
    {
        
        var settingPath = @"D:\ConfigFiles\C#\OTCManagementSystem\appsettings.json";
        int pathNums, stepNums;
        string? arch = null;
        try
        {
            pathNums = (int)Utils.ReadSetting(settingPath, "PricingEngine", "PathNums")!;
            stepNums = (int)Utils.ReadSetting(settingPath, "PricingEngine", "StepNums")!;
            arch = (string)Utils.ReadSetting(settingPath, "PricingEngine", "Architecture")!;
        }

        catch (Exception ex)
        {
            pathNums = 1 << 16;
            stepNums = 750;
            Console.WriteLine($"failed to read settings from {settingPath}, using default values: {ex.Message}");
        }

        arch ??= "CPU";
        PathSimulator.Init(pathNums, stepNums, arch);
    }
}