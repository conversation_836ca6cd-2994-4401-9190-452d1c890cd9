using System.ComponentModel.DataAnnotations;
using DerivativePricing.Domain.Models;
using DerivativePricing.Domain.Interfaces;
using FinEnumerations;

namespace DerivativePricing.Core.Pricers;

public class AccumulatorPricer : IPricing<AccumulatorOption>
{
    public DerivativeResult CalculatePrice(AccumulatorOption option, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        DerivativeResult result;
        if (mode != CalculationMode.ValueOnly)
        {
            result = AccumulatorGreeks(option, underlyingSpotPrice, mode);
            return result;
        }

        result = AccumulatorPrice(option, underlyingSpotPrice);
        return result;
    }

    public DerivativeResult CalculatePrice(AccumulatorOption option, double[] underlyingSpotPrices,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        throw new ValidationException("AccumulatorOption does not support multi-asset pricing");
    }


    private DerivativeResult AccumulatorPrice(AccumulatorOption option, double underlyingSpotPrice)
    {
        var price = 0.0;
        var accAtExpiry = false || option.ExpiryObservationFactor is not null and { Count: > 0 };
        var realGears = option.Gearing;
        var expiryGears = option.Gearing;
        if (accAtExpiry)
        {
            realGears[option.ContractType == ContractType.Call ? 0 : ^1] = 0;
            expiryGears[option.ContractType == ContractType.Call ? 0 : ^1] =
                option.Gearing[option.ContractType == ContractType.Call ? 0 : ^1] * option.InitialAccumulatingDays;
        }
        var barrierPricer = new BarrierPricer();
        foreach (var day in AcMaturities(option.DaysToExpiration, option.InitialAccumulatingDays))
        {
            var gear = option.DaysToExpiration == day
                ? expiryGears
                : realGears;
            var optPrice = ComboValue(option, underlyingSpotPrice, day, gear, barrierPricer);
            price += optPrice;
        }

        return new DerivativeResult
        {
            BusiNo = option.BusiNo,
            Value = price,
            Delta = [double.NaN],
            Gamma = [double.NaN],
            Vega = [double.NaN],
            Theta = double.NaN,
            Rho = double.NaN,
            Volatility = [option.Volatility],
            BasisDiscount = [option.BasisDiscount],
            UnderlyingSpotPrice = [underlyingSpotPrice]
        };
    }

    private DerivativeResult AccumulatorGreeks(AccumulatorOption option, double underlyingSpotPrice,
        CalculationMode mode)
    {
        var accAtExpiry = false || option.ExpiryObservationFactor is not null and { Count: > 0 };
        var realGears = option.Gearing;
        var expiryGears = option.Gearing;
        if (accAtExpiry)
        {
            realGears[option.ContractType == ContractType.Call ? 0 : ^1] = 0;
            expiryGears[option.ContractType == ContractType.Call ? 0 : ^1] =
                option.Gearing[option.ContractType == ContractType.Call ? 0 : ^1] * option.InitialAccumulatingDays;
        }

        double finalPrice = 0, finalDelta = 0, finalGamma = 0, finalVega = 0, finalTheta = 0, finalRho = 0;
        var barrierPricer = new BarrierPricer();
        foreach (var day in AcMaturities(option.DaysToExpiration, option.InitialAccumulatingDays))
        {
            var gear = option.DaysToExpiration == day
                ? expiryGears
                : realGears;
            var (optPrice, delta, gamma, vega, theta, rho) =
                ComboGreeks(option, underlyingSpotPrice, day, gear, barrierPricer, mode);
            finalPrice += optPrice;
            finalDelta += delta;
            finalGamma += gamma;
            finalVega += vega;
            finalTheta += theta;
            finalRho += rho;
        }

        return new DerivativeResult
        {
            Value = finalPrice,
            Delta = [finalDelta],
            Gamma = [finalGamma],
            Vega = [finalVega],
            Theta = finalTheta,
            Rho = finalRho,
            Volatility = [option.Volatility],
            BasisDiscount = [option.BasisDiscount],
            UnderlyingSpotPrice = [underlyingSpotPrice]
        };
    }

    private double ComboValue(AccumulatorOption option, double underlyingSpotPrice, int daysLeft,
        List<double> gear, BarrierPricer barrierPricer)
    {
        double price = 0;
        for (int i = 0; i < option.StrikePrices.Count; i++)
        {
            var barrier = new BarrierOption
            {
                StrikePrice = option.StrikePrices[i],
                BarrierLevel = option.BarrierLevels[i],
                KnockType = option.KnockTypes[i],
                ContractType = option.ContractTypes[i],
                KnockStatus = false,
                Rebate = option.Rebates[i],
                ObservationFrequency = Frequency.Expiry,
                RiskFreeRate = option.RiskFreeRate,
                DividendYield = option.DividendYield,
                DaysToExpiration = daysLeft,
                AnnualDenominator = option.AnnualDenominator,
                ParticipationRate = option.ParticipationRate,
                BasisDiscount = option.BasisDiscount,
                UnderlyingType = option.UnderlyingType,
                Volatility = option.Volatility,
                IntraDayFraction = option.IntraDayFraction,
                ExerciseType = option.ExerciseType,
                OptionType = OptionType.Barrier,
                PricingMethod = PricingMethod.Analytic,
            };
            var optValue = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice).Value;
            price += optValue * gear[i];
        }

        return price;
    }

    private (double optiValue, double delta, double gamma, double vega, double theta, double rho) ComboGreeks(
        AccumulatorOption option, double underlyingSpotPrice, int daysLeft, List<double> gears,
        BarrierPricer barrierPricer, CalculationMode mode = CalculationMode.ValueOnly)
    {
        double optValue = 0, delta = 0, gamma = 0, vega = 0, theta = 0, rho = 0;

        for (int i = 0; i < option.StrikePrices.Count; i++)
        {
            var barrier = new BarrierOption
            {
                StrikePrice = option.StrikePrices[i],
                BarrierLevel = option.BarrierLevels[i],
                KnockType = option.KnockTypes[i],
                ContractType = option.ContractTypes[i],
                KnockStatus = false,
                Rebate = option.Rebates[i],
                ObservationFrequency = Frequency.Expiry,
                RiskFreeRate = option.RiskFreeRate,
                DividendYield = option.DividendYield,
                DaysToExpiration = daysLeft,
                AnnualDenominator = option.AnnualDenominator,
                ParticipationRate = option.ParticipationRate,
                BasisDiscount = option.BasisDiscount,
                UnderlyingType = option.UnderlyingType,
                Volatility = option.Volatility,
                IntraDayFraction = option.IntraDayFraction,
                ExerciseType = option.ExerciseType,
                OptionType = OptionType.Barrier,
                PricingMethod = PricingMethod.Analytic,
            };
            var greeksResult = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
            optValue += greeksResult.Value * gears[i];
            delta += greeksResult.Delta[0] * gears[i];
            gamma += greeksResult.Gamma[0] * gears[i];
            vega += greeksResult.Vega[0] * gears[i];
            theta += greeksResult.Theta * gears[i];
            rho += greeksResult.Rho * gears[i];
        }

        return (optValue, delta, gamma, vega, theta, rho);
    }

    private IEnumerable<int> AcMaturities(int daysToExpiration, int initialAccumulatingDays)
    {
        var daysBeforeAcc = daysToExpiration - initialAccumulatingDays;
        daysBeforeAcc = daysBeforeAcc switch
        {
            0 => 1,
            < 0 => 0,
            _ => daysBeforeAcc + 1
        };

        for (int i = daysBeforeAcc; i <= daysToExpiration; i++)
        {
            yield return i;
        }
    }

    // private  int[] AcMaturities(int daysToExpiration, int initialAccumulatingDays)
    // {
    //     int start = daysToExpiration - initialAccumulatingDays;
    //
    //     if (start <= 0)
    //     {
    //         start = start == 0 ? 1 : 0;
    //     }
    //     else
    //     {
    //         start++;
    //     }
    //
    //     int length = daysToExpiration - start + 1;
    //     int[] result = new int[length];
    //
    //     for (int i = 0; i < length; i++)
    //     {
    //         result[i] = start + i;
    //     }
    //
    //     return result;
    // }
}
