using DerivativePricing.Domain.Models;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Core.Internals;
using FinEnumerations;
using MathNet.Numerics.Distributions;

namespace DerivativePricing.Core.Pricers;

public partial class VanillaPricer
{
    public double BSAmericanApprox2002(VanillaOption option, double underlyingSpot)
    {
        double S, X, r, b, T = option.Maturity, v = option.Volatility;
    b = Utils.CarryCost(option);

    if (option.ContractType == ContractType.Call)
    {
        S = underlyingSpot;
        X = option.StrikePrice;
        r = option.RiskFreeRate;
    }
    else
    {
        S = option.StrikePrice;
        X = underlyingSpot;
        r = option.RiskFreeRate - b;
        b = -b;
    }

    var t1 = 0.5 * (Math.Sqrt(5.0) - 1.0) * T;

    if (b >= r)
        return BsmPrice(option, underlyingSpot).Item1;

    var v2 = v * v;
    var bOverV2 = b / v2;
    var rOverV2 = r / v2;
    var Beta = (0.5 - bOverV2) + Math.Sqrt((bOverV2 - 0.5) * (bOverV2 - 0.5) + 2.0 * rOverV2);
    var BInfinity = Beta / (Beta - 1.0) * X;
    var B0 = Math.Max(X, r / (r - b) * X);

    var X2 = X * X;
    var BDiff = BInfinity - B0;
    var ht1 = -(b * t1 + 2.0 * v * Math.Sqrt(t1)) * X2 / (BDiff * B0);
    var ht2 = -(b * T + 2.0 * v * Math.Sqrt(T)) * X2 / (BDiff * B0);

    var expHt1 = Math.Exp(ht1);
    var expHt2 = Math.Exp(ht2);

    var I1 = B0 + BDiff * (1.0 - expHt1);
    var I2 = B0 + BDiff * (1.0 - expHt2);

    var I1MinusX = I1 - X;
    var I2MinusX = I2 - X;
    var alfa1 = I1MinusX * Math.Exp(-Beta * Math.Log(I1));
    var alfa2 = I2MinusX * Math.Exp(-Beta * Math.Log(I2));

    if (S >= I2)
        return S - X;

    double powS_Beta = Math.Exp(Beta * Math.Log(S));

    return alfa2 * powS_Beta
           - alfa2 * Phi(S, t1, Beta, I2, I2, r, b, v)
           + Phi(S, t1, 1.0, I2, I2, r, b, v)
           - Phi(S, t1, 1.0, I1, I2, r, b, v)
           - X * Phi(S, t1, 0.0, I2, I2, r, b, v)
           + X * Phi(S, t1, 0.0, I1, I2, r, b, v)
           + alfa1 * Phi(S, t1, Beta, I1, I2, r, b, v)
           - alfa1 * Ksi(S, T, Beta, I1, I2, I1, t1, r, b, v)
           + Ksi(S, T, 1.0, I1, I2, I1, t1, r, b, v)
           - Ksi(S, T, 1.0, X, I2, I1, t1, r, b, v)
           - X * Ksi(S, T, 0.0, I1, I2, I1, t1, r, b, v)
           + X * Ksi(S, T, 0.0, X, I2, I1, t1, r, b, v);
        
    }

    // Placeholder for phi function
    public static double Phi(double S, double T, double gamma, double h, double i, double r, double b, double v)
    {
        var v2 = v * v;
        var sqrtT = Math.Sqrt(T);
        var logS_h = Math.Log(S / h);
        var gamma_b = b + (gamma - 0.5) * v2;
        var d = -(logS_h + gamma_b * T) / (v * sqrtT);
        var kappa = 2.0 * b / v2 + 2.0 * gamma - 1.0;
        var lambdaT = (-r + gamma * b + 0.5 * gamma * (gamma - 1.0) * v2) * T;
        var powS_gamma = Math.Exp(gamma * Math.Log(S));
        var pow_i_S_kappa = Math.Exp(kappa * Math.Log(i / S));
        var d2 = d - 2.0 * Math.Log(i / S) / (v * sqrtT);

        return Math.Exp(lambdaT) * powS_gamma *
               (Normal.CDF(0, 1, d) - pow_i_S_kappa * Normal.CDF(0, 1, d2));
    }

    // Placeholder for ksi function
    public static double Ksi(double S, double T2, double gamma, double h, double I2, double I1, double t1,
        double r, double b, double v)
    {
        var v2 = v * v;
        var sqrtT1 = Math.Sqrt(t1);
        var sqrtT2 = Math.Sqrt(T2);
        var vSqrtT1 = v * sqrtT1;
        var vSqrtT2 = v * sqrtT2;
        var gamma_b = b + (gamma - 0.5) * v2;

        var logS_I1 = Math.Log(S / I1);
        var logI2S_I1 = Math.Log(I2 * I2 / (S * I1));
        var logS_h = Math.Log(S / h);
        var logI2S_h = Math.Log(I2 * I2 / (S * h));
        var logI1S_h = Math.Log(I1 * I1 / (S * h));
        var logSI1I1_hI2I2 = Math.Log(S * I1 * I1 / (h * I2 * I2));

        var e1 = (logS_I1 + gamma_b * t1) / vSqrtT1;
        var e2 = (logI2S_I1 + gamma_b * t1) / vSqrtT1;
        var e3 = (logS_I1 - gamma_b * t1) / vSqrtT1;
        var e4 = (logI2S_I1 - gamma_b * t1) / vSqrtT1;

        var f1 = (logS_h + gamma_b * T2) / vSqrtT2;
        var f2 = (logI2S_h + gamma_b * T2) / vSqrtT2;
        var f3 = (logI1S_h + gamma_b * T2) / vSqrtT2;
        var f4 = (logSI1I1_hI2I2 + gamma_b * T2) / vSqrtT2;

        var rho = sqrtT1 / sqrtT2;
        var lambda = -r + gamma * b + 0.5 * gamma * (gamma - 1.0) * v2;
        var kappa = 2.0 * b / v2 + 2.0 * gamma - 1.0;

        var powS_gamma = Math.Exp(gamma * Math.Log(S));
        var powI2S_kappa = Math.Exp(kappa * Math.Log(I2 / S));
        var powI1S_kappa = Math.Exp(kappa * Math.Log(I1 / S));
        var powI1I2_kappa = Math.Exp(kappa * Math.Log(I1 / I2));

        var expLambdaT2 = Math.Exp(lambda * T2);

        return expLambdaT2 * powS_gamma *
               (Utils.BivariateCDF(-e1, -f1, rho)
                - powI2S_kappa * Utils.BivariateCDF(-e2, -f2, rho)
                - powI1S_kappa * Utils.BivariateCDF(-e3, -f3, -rho)
                + powI1I2_kappa * Utils.BivariateCDF(-e4, -f4, -rho));
    }
}