using System.ComponentModel.DataAnnotations;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using DerivativePricing.Core.Internals;
using FinEnumerations;

namespace DerivativePricing.Core.Pricers;

public class AsianPricer : IPricing<AsianOption>
{
    public DerivativeResult CalculatePrice(AsianOption option, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        double price, delta = double.NaN, gamma = double.NaN, vega = double.NaN, theta = double.NaN, rho = double.NaN;
        
        if (option is { AverageType: AverageType.Arithmetic, PricingMethod: PricingMethod.MonteCarlo })
        {
            var (mu, steps, dt, sigma, parallelOpt) = PathSimulator.McFactor(option);
            var simPaths = PathSimulator.Simulate(underlyingSpotPrice, mu, sigma, dt, option.PathNums, steps);
            price = McPrice(option, simPaths, option.PathNums, parallelOpt);
            if (mode != CalculationMode.ValueOnly)
            {
                Func<AsianOption, double, DerivativeResult> calc = (opt, spot) =>
                    CalculatePrice(opt, spot, CalculationMode.ValueOnly);
                (delta, gamma) = Utils.FiniteDiffDeltaGamma(option, price, underlyingSpotPrice, calc);
                vega = Utils.FiniteDiffVega(option, underlyingSpotPrice, calc);
                theta = FiniteDiffThetaDaily(option, price * option.ParticipationRate, underlyingSpotPrice);
                if (mode == CalculationMode.AllGreeks)
                    rho = Utils.FiniteDiffRho(option, underlyingSpotPrice, calc);
            }

            return new DerivativeResult()
            {
                BusiNo = option.BusiNo,
                Value = price * option.ParticipationRate,
                Delta = [delta],
                Gamma = [gamma],
                Vega = [vega],
                Theta = theta,
                Rho = rho,
                Volatility = [option.Volatility],
                UnderlyingSpotPrice = [underlyingSpotPrice],
                BasisDiscount = [option.BasisDiscount]
            };
        }

        throw new NotImplementedException("Only arithmetic average with Monte Carlo method is implemented.");
    }

    public DerivativeResult CalculatePrice(AsianOption option, double[] underlyingSpotPrices,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        throw new ValidationException("Asian option does not support multi-asset pricing");
    }
    
    private double FiniteDiffThetaDaily(AsianOption asian, double optionValue, double underlyingSpotPrice)
    {
        var remainingDays = asian.DaysToExpiration - 1;
        if (remainingDays <= 0)
            return 0.0;
        var newFixingPrice = 0.0;
        if (asian.DaysToExpiration < asian.FixingPeriodLength)
        {
            newFixingPrice = asian.FixingPrice + underlyingSpotPrice / asian.FixingPeriodLength;
        }
      
        var nextAsian = asian with { DaysToExpiration = remainingDays, FixingPrice = newFixingPrice};
        var nextValue = CalculatePrice(nextAsian, underlyingSpotPrice, CalculationMode.ValueOnly).Value;
        var theta = nextValue - optionValue;
        return theta;

    }

    private double AsianCallPayoff(AsianOption asian, Span<double> underlyingSpotPrices)
    {
        var sum = 0.0;
        for (var i = 0; i < underlyingSpotPrices.Length; i++)
        {
            sum += underlyingSpotPrices[i];
        }

        var average = sum / asian.FixingPeriodLength + asian.FixingPrice;
        return Math.Max(average - asian.StrikePrice, 0) + asian.GuaranteedPayoff;
    }

    private double AsianPutPayoff(AsianOption asian, Span<double> underlyingSpotPrices)
    {
        var sum = 0.0;
        for (var i = 0; i < underlyingSpotPrices.Length; i++)
        {
            sum += underlyingSpotPrices[i];
        }

        var average = sum / asian.FixingPeriodLength + asian.FixingPrice;
        return Math.Max(asian.StrikePrice - average, 0) + asian.GuaranteedPayoff;
    }

    private double McPrice(AsianOption asian, double[] simPaths, int pathNums, ParallelOptions parallelOpt)
    {
        var price = 0.0;
        var pathLength = asian.DaysToExpiration + 1;
        Parallel.For((long)0, pathNums, parallelOpt, () => 0.0, (i, state, local) =>
        {
            var singlePath = simPaths.AsSpan((int)(i * pathLength), pathLength);
            Span<double> effectiveSpotPrices;
            if (asian.DaysToExpiration < asian.FixingPeriodLength)
            {
                effectiveSpotPrices = singlePath;
            }
            else
            {
                var memoryLength = singlePath.Length - asian.FixingPeriodLength;
                effectiveSpotPrices = singlePath[memoryLength..];
            }

            local += asian.ContractType == ContractType.Call
                ? AsianCallPayoff(asian, effectiveSpotPrices)
                : AsianPutPayoff(asian, effectiveSpotPrices);

            return local;
        }, local =>
        {
            lock (parallelOpt)
            {
                price += local;
            }
        });

        var discount = Math.Exp(-asian.RiskFreeRate * asian.Maturity);
        return price / pathNums * discount;
    }

    private double McPriceTest(AsianOption asian, double[] simPaths, int pathNums, ParallelOptions parallelOpt)
    {
        var price = 0.0;
        var pathLength = asian.DaysToExpiration + 1;

        for (int i = 0; i < pathNums; i++)
        {
            var singlePath = simPaths.AsSpan(i * pathLength, pathLength);
            Span<double> effectiveSpotPrices;
            if (asian.DaysToExpiration < asian.FixingPeriodLength)
            {
                effectiveSpotPrices = singlePath;
            }
            else
            {
                var fixLength = singlePath.Length - asian.FixingPeriodLength;
                effectiveSpotPrices = singlePath[fixLength..];
            }

            price += asian.ContractType == ContractType.Call
                ? AsianCallPayoff(asian, effectiveSpotPrices)
                : AsianPutPayoff(asian, effectiveSpotPrices);
        }

        var discount = Math.Exp(-asian.RiskFreeRate * asian.Maturity);
        return price / pathNums * discount;
    }
}