using System.ComponentModel.DataAnnotations;
using DerivativePricing.Core.Internals;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using MathNet.Numerics.Distributions;
using FinEnumerations;

namespace DerivativePricing.Core.Pricers;

public class BarrierPricer : IPricing<BarrierOption>
{
    public DerivativeResult CalculatePrice(BarrierOption barrier, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        var price = barrier is { KnockStatus: true, KnockType: KnockType.DownOut or KnockType.UpOut }
            ? barrier.Rebate
            : barrier.ObservationFrequency == Frequency.Expiry
                ? ExpiryObservationNpv(barrier, underlyingSpotPrice)
                : barrier.KnockType is KnockType.DownIn or KnockType.UpIn
                    ? KiNpv(barrier, underlyingSpotPrice)
                    : KoNpv(barrier, underlyingSpotPrice);
        double delta = double.NaN, gamma = double.NaN, vega = double.NaN, theta = double.NaN, rho = double.NaN;
        if (mode != CalculationMode.ValueOnly)
        {
            Func<BarrierOption, double, DerivativeResult> calc = (opt, spot) =>
                CalculatePrice(opt, spot, CalculationMode.ValueOnly);
            (delta, gamma) = Utils.FiniteDiffDeltaGamma<BarrierOption>(barrier, price, underlyingSpotPrice, calc);
            vega = Utils.FiniteDiffVega<BarrierOption>(barrier, underlyingSpotPrice, calc);
            theta = Utils.FiniteDiffTheta<BarrierOption>(barrier, underlyingSpotPrice, calc);
            if (mode == CalculationMode.AllGreeks)
                rho = Utils.FiniteDiffRho<BarrierOption>(barrier, underlyingSpotPrice, calc);
        }

        return new DerivativeResult
        {
            BusiNo = barrier.BusiNo,
            Value = price * barrier.ParticipationRate,
            Delta = [delta],
            Gamma = [gamma],
            Vega = [vega],
            Theta = theta,
            Rho = rho ,
            Volatility = [barrier.Volatility],
            BasisDiscount = [barrier.BasisDiscount],
            UnderlyingSpotPrice = [underlyingSpotPrice]
        };
    }

    public DerivativeResult CalculatePrice(BarrierOption option, double[] underlyingSpotPrices,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        throw new ValidationException("BarrierOption does not support multi-asset pricing");
    }

    public DerivativeResult CalculatePrice(BarrierOption barrier, double underlyingSpotPrice)
    {
        var price = barrier is { KnockStatus: true, KnockType: KnockType.DownOut or KnockType.UpOut }
            ? barrier.Rebate
            : barrier.ObservationFrequency == Frequency.Expiry
                ? ExpiryObservationNpv(barrier, underlyingSpotPrice)
                : barrier.KnockType is KnockType.DownIn or KnockType.UpIn
                    ? KiNpv(barrier, underlyingSpotPrice)
                    : KoNpv(barrier, underlyingSpotPrice);

        return new DerivativeResult
        {
            Value = price * barrier.ParticipationRate,
            Delta = [double.NaN],
            Gamma = [double.NaN],
            Vega = [double.NaN],
            Theta = double.NaN,
            Rho = double.NaN,
            Volatility = [barrier.Volatility],
            BasisDiscount = [barrier.BasisDiscount],
            UnderlyingSpotPrice = [underlyingSpotPrice]
        };
    }


    public static double ShiftBarrier(double barrierLevel, double spotPrice, double volatility,
        Frequency observationFrequency, int annualDenominator)
    {
        if (observationFrequency == Frequency.Continuous)
            return barrierLevel;
        var deltaDay = observationFrequency switch
        {
            Frequency.Daily => 1.0,
            Frequency.Weekly => 5.0,
            Frequency.BiWeekly => 10.0,
            Frequency.Monthly => 22.0,
            Frequency.BiMonthly => 24.0,
            Frequency.Quarterly => 66.0,
            Frequency.HalfYearly => 121.0,
            Frequency.Yearly => 243.0,
            Frequency.TriWeekly => 15.0,
            _ => throw new ArgumentOutOfRangeException()
        };
        var deltaT = deltaDay / annualDenominator;
        var barrierSign = barrierLevel > spotPrice ? 1.0 : -1.0;
        var discreteBarrier = barrierLevel * Math.Exp(barrierSign * 0.5826 * volatility *
                                                      Math.Sqrt(deltaT));
        return discreteBarrier;
    }

    private (double, double, double, double, double, double) PricingFactor(BarrierOption barrier,
        double spotPrice)
    {
        // Determine eta and phi based on contract and knock type
        // var shiftedBarrier = ShiftBarrier(barrier, spotPrice);
        var shiftedBarrier = ShiftBarrier(barrier.BarrierLevel, spotPrice, barrier.Volatility,
            barrier.ObservationFrequency, barrier.AnnualDenominator);
        var carryCost = Utils.CarryCost(barrier);
        double eta = (barrier.KnockType is KnockType.DownIn or KnockType.DownOut) ? 1 : -1;
        double phi = barrier.ContractType == ContractType.Call ? 1 : -1;

        var squareVol = barrier.Volatility * barrier.Volatility;
        var sqrtMaturity = Math.Sqrt(barrier.Maturity);
        var volSqrtMaturity = barrier.Volatility * sqrtMaturity;
        var discounter = Math.Exp(-barrier.RiskFreeRate * barrier.Maturity);

        var mu = (carryCost - 0.5 * squareVol) / squareVol;
        var lambda = Math.Sqrt(mu * mu + 2 * barrier.RiskFreeRate / squareVol);

        // Precompute common expressions
        var spotRatio = shiftedBarrier / spotPrice;
        var spotRatio2Mu = Math.Pow(spotRatio, 2 * mu);
        var spotRatio2MuPlus2 = spotRatio2Mu * Math.Pow(spotRatio, 2);

        var x1 = Math.Log(spotPrice / barrier.StrikePrice) / volSqrtMaturity + (1 + mu) * volSqrtMaturity;
        var x2 = Math.Log(spotPrice / shiftedBarrier) / volSqrtMaturity + (1 + mu) * volSqrtMaturity;
        var y1 = Math.Log(shiftedBarrier * shiftedBarrier / (spotPrice * barrier.StrikePrice)) / volSqrtMaturity +
                 (1 + mu) * volSqrtMaturity;
        var y2 = Math.Log(shiftedBarrier / spotPrice) / volSqrtMaturity + (1 + mu) * volSqrtMaturity;
        var Z = Math.Log(shiftedBarrier / spotPrice) / volSqrtMaturity + lambda * volSqrtMaturity;

        // Precompute common CDF values
        var cdfPhiX1 = Normal.CDF(0, 1, phi * x1);
        var cdfPhiX1MinusVol = Normal.CDF(0, 1, phi * x1 - phi * volSqrtMaturity);
        var cdfPhiX2 = Normal.CDF(0, 1, phi * x2);
        var cdfPhiX2MinusVol = Normal.CDF(0, 1, phi * x2 - phi * volSqrtMaturity);
        var cdfEtaY1 = Normal.CDF(0, 1, eta * y1);
        var cdfEtaY1MinusVol = Normal.CDF(0, 1, eta * y1 - eta * volSqrtMaturity);
        var cdfEtaY2 = Normal.CDF(0, 1, eta * y2);
        var cdfEtaY2MinusVol = Normal.CDF(0, 1, eta * y2 - eta * volSqrtMaturity);
        var cdfEtaX2MinusVol = Normal.CDF(0, 1, eta * x2 - eta * volSqrtMaturity);
        var cdfEtaZ = Normal.CDF(0, 1, eta * Z);
        var cdfEtaZMinus2Lambda = Normal.CDF(0, 1, eta * Z - 2 * eta * lambda * volSqrtMaturity);

        var temp = phi * spotPrice * Math.Exp((carryCost - barrier.RiskFreeRate) * barrier.Maturity);
        var phiStrikePriceDiscounter = phi * barrier.StrikePrice * discounter;

        var a = temp * cdfPhiX1 - phiStrikePriceDiscounter * cdfPhiX1MinusVol;
        var b = temp * cdfPhiX2 - phiStrikePriceDiscounter * cdfPhiX2MinusVol;
        var c = temp * spotRatio2MuPlus2 * cdfEtaY1 - phiStrikePriceDiscounter * spotRatio2Mu * cdfEtaY1MinusVol;
        var d = temp * spotRatio2MuPlus2 * cdfEtaY2 - phiStrikePriceDiscounter * spotRatio2Mu * cdfEtaY2MinusVol;
        var e = barrier.Rebate * discounter * (cdfEtaX2MinusVol - spotRatio2Mu * cdfEtaY2MinusVol);
        var f = barrier.Rebate * (Math.Pow(spotRatio, mu + lambda) * cdfEtaZ +
                                  Math.Pow(spotRatio, mu - lambda) * cdfEtaZMinus2Lambda);

        return (a, b, c, d, e, f);
    }

    private double KiNpv(BarrierOption barrier, double spotPrice)
    {
        var vanillaPricer = new VanillaPricer();
        if (barrier.Maturity <= 0)
        {
            // knock-in is excluding boundary
            if ((barrier.KnockType == KnockType.UpIn && spotPrice < barrier.BarrierLevel) ||
                (barrier.KnockType == KnockType.DownIn && spotPrice > barrier.BarrierLevel))
                return barrier.Rebate;
            return vanillaPricer.EuropeanPayoff(barrier.StrikePrice, spotPrice, barrier.ContractType);
        }


        if (barrier.KnockStatus)
            return vanillaPricer.BsmPrice(barrier, spotPrice).Item1;

        var shiftedBarrier = ShiftBarrier(barrier.BarrierLevel, spotPrice, barrier.Volatility,
            barrier.ObservationFrequency, barrier.AnnualDenominator);
        var (a, b, c, d, e, f) = PricingFactor(barrier, spotPrice);

        return barrier.ContractType == ContractType.Call
            ? barrier.KnockType switch
            {
                KnockType.DownIn => barrier.StrikePrice > shiftedBarrier ? c + e : a - b + d + e,
                KnockType.UpIn => barrier.StrikePrice > shiftedBarrier ? a + e : b - c + d + e,
                _ => double.NaN
            }
            : barrier.KnockType switch
            {
                KnockType.DownIn => barrier.StrikePrice > shiftedBarrier ? b - c + d + e : a + e,
                KnockType.UpIn => barrier.StrikePrice > shiftedBarrier ? a - b + d + e : c + e,
                _ => double.NaN
            };
    }

    private double KoNpv(BarrierOption barrier, double spotPrice)
    {
        if (barrier.Maturity <= 0)
        {
            // knock-out is boundary inclusive
            var vanillaPricer = new VanillaPricer();
            if ((barrier.KnockType == KnockType.UpOut && spotPrice >= barrier.BarrierLevel) ||
                (barrier.KnockType == KnockType.DownOut && spotPrice <= barrier.BarrierLevel))
                return barrier.Rebate;

            return vanillaPricer.EuropeanPayoff(spotPrice, barrier.StrikePrice, barrier.ContractType);
        }

        if (barrier.KnockStatus ||
            (barrier.KnockType == KnockType.UpOut && spotPrice >= barrier.BarrierLevel) ||
            (barrier.KnockType == KnockType.DownOut && spotPrice <= barrier.BarrierLevel))
        {
            return barrier.Rebate;
        }


        var (a, b, c, d, e, f) = PricingFactor(barrier, spotPrice);
        var shiftedBarrier = ShiftBarrier(barrier.BarrierLevel, spotPrice, barrier.Volatility,
            barrier.ObservationFrequency, barrier.AnnualDenominator);
        return barrier.ContractType == ContractType.Call
            ? barrier.KnockType switch
            {
                KnockType.DownOut => barrier.StrikePrice > shiftedBarrier ? a - c + f : b - d + f,
                KnockType.UpOut => barrier.StrikePrice > shiftedBarrier ? f : a - b + c - d + f,
                _ => double.NaN
            }
            : barrier.KnockType switch
            {
                KnockType.DownOut => barrier.StrikePrice > shiftedBarrier ? a - b + c - d + f : f,
                KnockType.UpOut => barrier.StrikePrice > shiftedBarrier ? b - d + f : a - c + f,
                _ => double.NaN
            };
    }

    private double SpreadOptionNpv(BarrierOption barrier, double lowerStrike, double upperStrike,
        double spotPrice)
    {
        if (lowerStrike >= upperStrike)
            throw new ArgumentException("Lower strike must be less than upper strike");

        VanillaOption vanilla = barrier;
        var vanillaPricer = new VanillaPricer();
        var lowerVanilla = vanilla with { StrikePrice = lowerStrike };
        var upperVanilla = vanilla with { StrikePrice = upperStrike };
        var lowerValue = vanillaPricer.BsmPrice(lowerVanilla, spotPrice).Item1;
        var upperValue = vanillaPricer.BsmPrice(upperVanilla, spotPrice).Item1;
        return barrier.ContractType switch
        {
            ContractType.Call => lowerValue - upperValue,
            ContractType.Put => upperValue - lowerValue,
            _ => throw new ArgumentOutOfRangeException()
        };
    }

    private double BinaryNpv(BarrierOption barrier, double spotPrice, double strikePrice)
    {
        var b = Utils.CarryCost(barrier);
        var volT = barrier.Volatility * Math.Sqrt(barrier.Maturity);
        double temp;
        if (barrier.UnderlyingType is SecType.CommodityFutures or SecType.CommodityIndexFutures
            or SecType.EquityIndexFutures)
        {
            temp = Math.Log(spotPrice / strikePrice) +
                   0.5 * barrier.Volatility * barrier.Volatility * barrier.Maturity;
        }
        else
        {
            temp = Math.Log(spotPrice / strikePrice) + (b + 0.5 * barrier.Volatility * barrier.Volatility);
        }

        var d1 = temp / volT;
        var d2 = d1 - volT;
        var optionPrice = barrier.ContractType == ContractType.Call
            ? Math.Exp(-barrier.RiskFreeRate * barrier.Maturity) * Normal.CDF(0, 1, d2)
            : Math.Exp(-barrier.RiskFreeRate * barrier.Maturity) * Normal.CDF(0, 1, -d2);
        return optionPrice;
    }

    private double ExpiryObservationNpv(BarrierOption barrier, double spotPrice)
    {
        var vanillaPricer = new VanillaPricer();
        if (barrier.Maturity <= 0)
        {
            if ((barrier.KnockType == KnockType.UpOut && spotPrice >= barrier.BarrierLevel) ||
                (barrier.KnockType == KnockType.DownOut && spotPrice <= barrier.BarrierLevel))
                return barrier.Rebate;

            return vanillaPricer.EuropeanPayoff(spotPrice, barrier.StrikePrice, barrier.ContractType);
        }

        double realBarrier = barrier.BarrierLevel <= 0 ? 0.001 : barrier.BarrierLevel;
        double optionValue;

        if (barrier.ContractType == ContractType.Call)
        {
            if (barrier.KnockType is KnockType.UpOut or KnockType.DownIn)
            {
                optionValue = realBarrier > barrier.StrikePrice
                    ? SpreadOptionNpv(barrier, barrier.StrikePrice, realBarrier, spotPrice) -
                      BinaryNpv(barrier, spotPrice, realBarrier) *
                      (Math.Abs(realBarrier - barrier.StrikePrice) - barrier.Rebate)
                    : BinaryNpv(barrier, spotPrice, realBarrier) * barrier.Rebate;
            }
            else
            {
                VanillaOption vanilla = barrier with { StrikePrice = realBarrier };
                optionValue = realBarrier > barrier.StrikePrice
                    ? vanillaPricer.BsmPrice(vanilla, spotPrice).Item1 + BinaryNpv(barrier, spotPrice, realBarrier) -
                      Math.Abs(realBarrier - barrier.StrikePrice)
                    : vanillaPricer.BsmPrice(vanilla, spotPrice).Item1;
            }
        }
        else
        {
            if (barrier.KnockType is KnockType.DownOut or KnockType.UpIn)
            {
                optionValue = realBarrier < barrier.StrikePrice
                    ? SpreadOptionNpv(barrier, realBarrier, barrier.StrikePrice, spotPrice) -
                      BinaryNpv(barrier, spotPrice, realBarrier) *
                      (Math.Abs(barrier.StrikePrice - realBarrier) - barrier.Rebate)
                    : BinaryNpv(barrier, spotPrice, realBarrier) * barrier.Rebate;
            }
            else
            {
                VanillaOption vanilla = barrier with { StrikePrice = realBarrier };
                optionValue = realBarrier < barrier.StrikePrice
                    ? vanillaPricer.BsmPrice(vanilla, spotPrice).Item1 + BinaryNpv(barrier, spotPrice, realBarrier) *
                    Math.Abs(barrier.StrikePrice - barrier.BarrierLevel)
                    : vanillaPricer.BsmPrice(barrier, spotPrice).Item1;
            }
        }

        return optionValue;
    }
}