using System.ComponentModel.DataAnnotations;
using DerivativePricing.Core.Internals;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using MathNet.Numerics.Distributions;

namespace DerivativePricing.Core.Pricers;

public class BinaryBarrierPricer : IPricing<BinaryBarrierOption>
{
    public DerivativeResult CalculatePrice(BinaryBarrierOption option, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        double price, delta = double.NaN, gamma = double.NaN, vega = double.NaN, theta = double.NaN, rho = double.NaN;
        price = AnalyticPrice(option, underlyingSpotPrice);
        if (mode != CalculationMode.ValueOnly)
        {
            Func<BinaryBarrierOption, double, DerivativeResult> calc = (opt, spot) =>
                CalculatePrice(opt, spot, CalculationMode.ValueOnly);
            (delta, gamma) = Utils.FiniteDiffDeltaGamma(option, price, underlyingSpotPrice, calc);
            vega = Utils.FiniteDiffVega(option, underlyingSpotPrice, calc);
            theta = Utils.FiniteDiffTheta(option, underlyingSpotPrice, calc);
            if (mode == CalculationMode.AllGreeks)
                rho = Utils.FiniteDiffRho(option, underlyingSpotPrice, calc);
        }

        return new DerivativeResult()
        {
            BusiNo = option.BusiNo,
            Value = price * option.ParticipationRate,
            Delta = [delta],
            Gamma = [gamma],
            Vega = [vega],
            Theta = theta,
            Rho = rho,
            Volatility = [option.Volatility],
            BasisDiscount = [option.BasisDiscount],
            UnderlyingSpotPrice = [underlyingSpotPrice]
        };
    }

    public DerivativeResult CalculatePrice(BinaryBarrierOption option, double[] underlyingSpotPrices,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        throw new ValidationException("BinaryBarrierPricer does not support multi-asset pricing");
    }
    
    private static Dictionary<string, double> PricingFactor(
        BinaryBarrierOption option, double underlyingSpotPrice)
    {
        // Implement the pricing factors calculation here
        var s = underlyingSpotPrice;
        var x = option.StrikePrice;
        var r = option.RiskFreeRate;
        var h = BarrierPricer.ShiftBarrier(option.BarrierLevel, underlyingSpotPrice, option.Volatility,
            option.ObservationFrequency, option.AnnualDenominator);
        var sqrtT = Math.Sqrt(option.Maturity);
        var vol = option.Volatility;
        var sqrVol = vol * vol;
        var carryCost = Utils.CarryCost(option);
        var u = (carryCost - sqrVol * 0.5) / sqrVol;
        var lambda = Math.Sqrt(u * u + 2 * r / sqrVol);
        var z = Math.Log(h / s) / vol / sqrtT + lambda * vol * sqrtT;

        var x1 = Math.Log(s / x) / (vol * sqrtT) + (u + 1) * vol * sqrtT;
        var x2 = Math.Log(s / h) / vol / sqrtT + (u + 1) * vol * sqrtT;
        var y1 = Math.Log(h * h / (s * x)) / vol / sqrtT + (u + 1) * vol * sqrtT;
        var y2 = Math.Log(h / s) / vol / sqrtT + (u + 1) * vol * sqrtT;
        return new Dictionary<string, double>()
        {
            { "x1", x1 },
            { "x2", x2 },
            { "y1", y1 },
            { "y2", y2 },
            { "u", u },
            { "lambda", lambda },
            { "z", z },
            { "b-r", carryCost - option.RiskFreeRate },
            { "sqrtT", sqrtT },
            { "shiftBarrier", h }
        };
    }

    private static double A1(BinaryBarrierOption option, double spotPrice, Dictionary<string, double> factor,
        double eta, double phi)
    {
        var x1 = factor["x1"];
        var u = factor["u"];
        var br = factor["b-r"];
        var result = spotPrice * Math.Exp(br * option.Maturity) * Normal.CDF(0, 1, phi * x1);
        return result;
    }

    private static double B1(BinaryBarrierOption option, double spotPrice, Dictionary<string, double> factor,
        double eta,
        double phi)
    {
        var x1 = factor["x1"];
        var sqrtT = factor["sqrtT"];
        var result = option.FixedPayment * Math.Exp(-option.RiskFreeRate * option.Maturity) *
                     Normal.CDF(0, 1, phi * (x1 - option.Volatility * sqrtT));
        return result;
    }

    private static double A2(BinaryBarrierOption option, double spotPrice, Dictionary<string, double> factor,
        double eta,
        double phi)
    {
        var x2 = factor["x2"];
        var br = factor["b-r"];
        var result = spotPrice * Math.Exp(br * option.Maturity) * Normal.CDF(0, 1, phi * x2);
        return result;
    }

    private static double B2(BinaryBarrierOption option, double spotPrice, Dictionary<string, double> factor,
        double eta,
        double phi)
    {
        var x2 = factor["x2"];
        var sqrtT = factor["sqrtT"];
        var result = option.FixedPayment * Math.Exp(-option.RiskFreeRate * option.Maturity) *
                     Normal.CDF(0, 1, phi * (x2 - option.Volatility * sqrtT));
        return result;
    }

    private static double A3(BinaryBarrierOption option, double spotPrice, Dictionary<string, double> factor,
        double eta,
        double phi)
    {
        var y1 = factor["y1"];
        var u = factor["u"];
        var br = factor["b-r"];
        var shiftedBarrier = factor["shiftBarrier"];
        var result = spotPrice * Math.Exp(br * option.Maturity) * Math.Pow(shiftedBarrier / spotPrice, 2 * (u + 1)) *
                     Normal.CDF(0, 1, phi * y1);
        return result;
    }

    private static double B3(BinaryBarrierOption option, double spotPrice, Dictionary<string, double> factor,
        double eta,
        double phi)
    {
        var y1 = factor["y1"];
        // var br = factor["b-r"];
        var sqrtT = factor["sqrtT"];
        var u = factor["u"];
        var shiftBarrier = factor["shiftBarrier"];
        var result = option.FixedPayment * Math.Exp(-option.RiskFreeRate * option.Maturity) *
                     Math.Pow(shiftBarrier / spotPrice, 2*u) *
                     Normal.CDF(0, 1, eta * (y1 - option.Volatility * sqrtT));
        var test = eta * (y1 - option.Volatility * sqrtT);
        return result;
    }

    private static double A4(BinaryBarrierOption option, double spotPrice, Dictionary<string, double> factor,
        double eta,
        double phi)
    {
        var y2 = factor["y2"];
        var u = factor["u"];
        var br = factor["b-r"];
        var shiftBarrier = factor["shiftBarrier"];
        var result = spotPrice * Math.Exp(br * option.Maturity) * Math.Pow(shiftBarrier / spotPrice, 2 * (u + 1)) *
                     Normal.CDF(0, 1, eta * y2);
        return result;
    }

    private static double B4(BinaryBarrierOption option, double spotPrice, Dictionary<string, double> factor,
        double eta,
        double phi)
    {
        var y2 = factor["y2"];
        var sqrtT = factor["sqrtT"];
        var u = factor["u"];
        var shiftBarrier = factor["shiftBarrier"];
        var result = option.FixedPayment * Math.Exp(-option.RiskFreeRate * option.Maturity) *
                     Math.Pow(shiftBarrier / spotPrice, 2 * u) *
                     Normal.CDF(0, 1, eta * (y2 - option.Volatility * sqrtT));
        return result;
    }

    private static double AnalyticPrice(BinaryBarrierOption option, double spotPrice)
    {
        var factors = PricingFactor(option, spotPrice);
        var shiftBarrier = factors["shiftBarrier"];
        var optPrice = 0.0;
        switch (option.KnockType, option.BinaryType, option.ContractType)
        {
            case (KnockType.DownIn, BinaryOptionType.CashOrNothing, ContractType.Call): //13
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice < shiftBarrier) // "in" does not include the bound
                        return spotPrice >= option.StrikePrice ? option.FixedPayment : 0.0;
                    else
                        return 0.0;

                if (option.StrikePrice > shiftBarrier)
                    optPrice = B3(option, spotPrice, factors, 1.0, 1.0);
                else
                    optPrice = B1(option, spotPrice, factors, 1.0, 1.0) - B2(option, spotPrice, factors, 1.0, 1.0) +
                               B4(option, spotPrice, factors, 1.0, 1.0);
                break;
            }
            case (KnockType.UpIn, BinaryOptionType.CashOrNothing, ContractType.Call): //14
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice > shiftBarrier) // "in" does not include the bound
                        return spotPrice >= option.StrikePrice ? option.FixedPayment : 0.0;
                    else
                        return 0.0;

                if (option.StrikePrice > shiftBarrier)
                    optPrice = B1(option, spotPrice, factors, -1.0, 1.0);
                else
                    optPrice = B2(option, spotPrice, factors, -1.0, 1.0) - B3(option, spotPrice, factors, -1.0, 1.0) +
                               B4(option, spotPrice, factors, -1.0, 1.0);
                break;
            }
            case (KnockType.DownIn, BinaryOptionType.AssetOrNothing, ContractType.Call): // 15
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice < shiftBarrier) // "in" does not include the bound
                        return spotPrice >= option.StrikePrice ? spotPrice : 0.0;
                    else
                        return 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = A3(option, spotPrice, factors, 1.0, 1.0);
                else
                    optPrice = A1(option, spotPrice, factors, 1.0, 1.0) - A2(option, spotPrice, factors, 1.0, 1.0) +
                               A4(option, spotPrice, factors, 1.0, 1.0);
                break;
            }
            case (KnockType.UpIn, BinaryOptionType.AssetOrNothing, ContractType.Call): // 16
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice > shiftBarrier) // "in" does not include the bound
                        return spotPrice >= option.StrikePrice ? spotPrice : 0.0;
                    else
                        return 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = A1(option, spotPrice, factors, 1.0, 1.0);
                else
                    optPrice = A2(option, spotPrice, factors, -1.0, 1.0) - A3(option, spotPrice, factors, -1.0, 1.0) +
                               A4(option, spotPrice, factors, -1.0, 1.0);
                break;
            }
            case (KnockType.DownIn, BinaryOptionType.CashOrNothing, ContractType.Put): // 17
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice < shiftBarrier) // "in" does not include the bound
                        return spotPrice <= option.StrikePrice ? option.FixedPayment : 0.0;
                    else
                        return 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = B2(option, spotPrice, factors, 1.0, -1.0) - B3(option, spotPrice, factors, 1.0, -1.0) +
                               B4(option, spotPrice, factors, 1.0, -1.0);
                else
                    optPrice = B1(option, spotPrice, factors, 1.0, -1.0);
                break;
            }
            case (KnockType.UpIn, BinaryOptionType.CashOrNothing, ContractType.Put): //18
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice > shiftBarrier) // "in" does not include the bound
                        return spotPrice <= option.StrikePrice ? option.FixedPayment : 0.0;
                    else
                        return 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = B1(option, spotPrice, factors, -1.0, -1.0) - B2(option, spotPrice, factors, -1.0, -1.0) +
                               B4(option, spotPrice, factors, -1.0, -1.0);
                else
                    optPrice = B3(option, spotPrice, factors, -1.0, -1.0);
                break;
            }
            case (KnockType.DownIn, BinaryOptionType.AssetOrNothing, ContractType.Put): // 19
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice < shiftBarrier) // "in" does not include the bound
                        return spotPrice <= option.StrikePrice ? spotPrice : 0.0;
                    else
                        return 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = A2(option, spotPrice, factors, 1, -1.0) - A3(option, spotPrice, factors, 1.0, -1.0) +
                               A4(option, spotPrice, factors, 1.0, -1.0);
                else
                    optPrice = A1(option, spotPrice, factors, 1.0, -1.0);
                break;
            }
            case (KnockType.UpIn, BinaryOptionType.AssetOrNothing, ContractType.Put): // 20
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice > shiftBarrier) // "in" does not include the bound
                        return spotPrice <= option.StrikePrice ? spotPrice : 0.0;
                    else
                        return 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = A1(option, spotPrice, factors, -1.0, -1.0) - A2(option, spotPrice, factors, -1.0, -1.0) +
                               A3(option, spotPrice, factors, -1.0, -1.0);
                else
                    optPrice = A3(option, spotPrice, factors, -1.0, -1.0);
                break;
            }
            case (KnockType.DownOut, BinaryOptionType.CashOrNothing, ContractType.Call): // 21
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice <= shiftBarrier) // "out" includes the bound
                        return option.Rebate;
                    else
                        return spotPrice >= option.StrikePrice ? option.FixedPayment : 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = B1(option, spotPrice, factors, 1.0, 1.0) - B3(option, spotPrice, factors, 1.0, 1.0);
                else
                    optPrice = B2(option, spotPrice, factors, 1.0, 1.0) - B4(option, spotPrice, factors, 1.0, 1.0);
                break;
            }
            case (KnockType.UpOut, BinaryOptionType.CashOrNothing, ContractType.Call): // 22
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice >= shiftBarrier) // "out" includes the bound
                        return option.Rebate;
                    else
                        return spotPrice >= option.StrikePrice ? option.FixedPayment : 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = 0.0;
                else
                    optPrice = B1(option, spotPrice, factors, -1.0, 1.0) - B2(option, spotPrice, factors, -1.0, 1.0) +
                               B3(option, spotPrice, factors, -1.0, 1.0) -
                               B4(option, spotPrice, factors, -1.0, 1.0);
                break;
            }
            case (KnockType.DownOut, BinaryOptionType.AssetOrNothing, ContractType.Call): // 23
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice <= shiftBarrier) // "out" includes the bound
                        return option.Rebate;
                    else
                        return spotPrice >= option.StrikePrice ? spotPrice : 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = A1(option, spotPrice, factors, 1.0, 1.0) - A3(option, spotPrice, factors, 1.0, 1.0);
                else
                    optPrice = A2(option, spotPrice, factors, 1.0, 1.0) - A4(option, spotPrice, factors, 1.0, 1.0);
                break;
            }
            case (KnockType.UpOut, BinaryOptionType.AssetOrNothing, ContractType.Call): // 24
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice >= shiftBarrier) // "out" includes the bound
                        return option.Rebate;
                    else
                        return spotPrice >= option.StrikePrice ? spotPrice : 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = 0.0;
                else
                    optPrice = A1(option, spotPrice, factors, -1.0, 1.0) - A2(option, spotPrice, factors, -1.0, 1.0) +
                               A3(option, spotPrice, factors, -1.0, 1.0) -
                               A4(option, spotPrice, factors, -1.0, 1.0);
                break;
            }
            case (KnockType.DownOut, BinaryOptionType.CashOrNothing, ContractType.Put): // 25
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice <= shiftBarrier) // "out" includes the bound
                        return option.Rebate;
                    else
                        return spotPrice <= option.StrikePrice ? option.FixedPayment : 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = B1(option, spotPrice, factors, 1.0, -1.0) - B2(option, spotPrice, factors, 1.0, -1.0) +
                               B3(option, spotPrice, factors, 1.0, -1.0) -
                               B4(option, spotPrice, factors, 1.0, -1.0);
                else
                    optPrice = 0.0;
                break;
            }
            case (KnockType.UpOut, BinaryOptionType.CashOrNothing, ContractType.Put): // 26
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice >= shiftBarrier) // "out" includes the bound
                        return option.Rebate;
                    else
                        return spotPrice <= option.StrikePrice ? option.FixedPayment : 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = B2(option, spotPrice, factors, -1.0, -1.0) - B4(option, spotPrice, factors, -1.0, -1.0);
                else
                    optPrice = B1(option, spotPrice, factors, -1.0, -1.0) - B3(option, spotPrice, factors, -1.0, -1.0);
                break;
            }
            case (KnockType.DownOut, BinaryOptionType.AssetOrNothing, ContractType.Put): // 27
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice <= shiftBarrier) // "out" includes the bound
                        return option.Rebate;
                    else
                        return spotPrice <= option.StrikePrice ? spotPrice : 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = A1(option, spotPrice, factors, 1.0, -1.0) - A2(option, spotPrice, factors, 1.0, -1.0) +
                               A3(option, spotPrice, factors, 1.0, -1.0) -
                               A4(option, spotPrice, factors, 1.0, -1.0);
                else
                    optPrice = 0.0;
                break;
            }
            case (KnockType.UpOut, BinaryOptionType.AssetOrNothing, ContractType.Put): // 28
            {
                if (option.Maturity < 1e-6)
                    if (option.KnockStatus || spotPrice >= shiftBarrier) // "out" includes the bound
                        return option.Rebate;
                    else
                        return spotPrice <= option.StrikePrice ? spotPrice : 0.0;
                if (option.StrikePrice > shiftBarrier)
                    optPrice = A2(option, spotPrice, factors, -1.0, -1.0) - A4(option, spotPrice, factors, -1.0, -1.0);
                else
                    optPrice = A1(option, spotPrice, factors, -1.0, -1.0) - A3(option, spotPrice, factors, -1.0, -1.0);
                break;
            }
            default:
                throw new InvalidOperationException("Unsupported option type");
        }

        return optPrice;
    }
}