using System.ComponentModel.DataAnnotations;
using DerivativePricing.Core.Internals;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using MathNet.Numerics.Distributions;

namespace DerivativePricing.Core.Pricers;

public class BinaryPricer : IPricing<BinaryOption>
{
    public DerivativeResult CalculatePrice(BinaryOption option, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        var price = AnalyticPrice(option, underlyingSpotPrice);
        double delta = double.NaN, gamma = double.NaN, vega = double.NaN, theta = double.NaN, rho = double.NaN;
        if (mode != CalculationMode.ValueOnly)
        {
            Func<BinaryOption, double, DerivativeResult> calc = (opt, spot) =>
                CalculatePrice(opt, spot, CalculationMode.ValueOnly);

            (delta, gamma) = Utils.FiniteDiffDeltaGamma<BinaryOption>(option, price, underlyingSpotPrice, calc);
            vega = Utils.FiniteDiffVega<BinaryOption>(option, underlyingSpotPrice, calc);
            theta = Utils.FiniteDiffTheta<BinaryOption>(option, underlyingSpotPrice, calc);
            if (mode == CalculationMode.AllGreeks)
                rho = Utils.FiniteDiffRho<BinaryOption>(option, underlyingSpotPrice, calc);
        }

        return new DerivativeResult
        {
            BusiNo = option.BusiNo,
            Value = price * option.ParticipationRate,
            Delta = [delta ],
            Gamma = [gamma ],
            Vega = [vega ],
            Theta = theta ,
            Rho = rho,
            Volatility = [option.Volatility],
            BasisDiscount = [option.BasisDiscount],
            UnderlyingSpotPrice = [underlyingSpotPrice]
        };
    }

    public DerivativeResult CalculatePrice(BinaryOption option, double[] underlyingSpotPrices,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        throw new ValidationException("BinaryOption does not support multi-asset pricing");
    }

    private double AnalyticPrice(BinaryOption option, double underlyingSpotPrice)
    {
        var (d1, d2, br) = CalculateD1D2BR(option, underlyingSpotPrice);
        var optValue = (option.ContractType, option.BinaryType) switch
        {
            (ContractType.Call, BinaryOptionType.CashOrNothing) => Normal.CDF(0, 1, d2),
            (ContractType.Call, _) => Normal.CDF(0, 1, d1),
            (ContractType.Put, BinaryOptionType.CashOrNothing) => Normal.CDF(0, 1, -d2),
            (ContractType.Put, _) => Normal.CDF(0, 1, -d1),
            _ => double.NaN
        };
        if (option.AnnualizedFixedPayment)
        {
            return option.FixedPayment * optValue * option.OriginalDaysLeft / (double)option.DayCount;
        }

        return option.BinaryType switch
        {
            BinaryOptionType.CashOrNothing => option.FixedPayment * optValue *
                                              Math.Exp(-option.RiskFreeRate * option.Maturity),
            _ => (option.PriceForSettlement == 0 ? underlyingSpotPrice : option.PriceForSettlement) * optValue *
                 Math.Exp(br * option.Maturity)
        };
    }

    private (double, double, double) CalculateD1D2BR(BinaryOption binary, double underlyingSpotPrice)
    {
        var volT = binary.Volatility * Math.Sqrt(binary.Maturity);
        return binary.UnderlyingType switch
        {
            SecType.CommodityFutures or SecType.EquityIndexFutures => (
                (Math.Log(underlyingSpotPrice / binary.StrikePrice) +
                 0.5 * binary.Volatility * binary.Volatility * binary.Maturity) / volT,
                (Math.Log(underlyingSpotPrice / binary.StrikePrice) +
                 0.5 * binary.Volatility * binary.Volatility * binary.Maturity) / volT - volT,
                0
            ),
            SecType.Stock or SecType.ETF or SecType.CommoditySpot => (
                (Math.Log(underlyingSpotPrice / binary.StrikePrice) +
                 (binary.RiskFreeRate - binary.DividendYield + 0.5 * binary.Volatility * binary.Volatility) *
                 binary.Maturity) / volT,
                (Math.Log(underlyingSpotPrice / binary.StrikePrice) +
                 (binary.RiskFreeRate - binary.DividendYield + 0.5 * binary.Volatility * binary.Volatility) *
                 binary.Maturity) / volT - volT,
                binary.RiskFreeRate - binary.DividendYield
            ),
            SecType.EquityIndex or SecType.CommodityIndex => (
                (Math.Log(underlyingSpotPrice / binary.StrikePrice) +
                 (-binary.BasisDiscount + 0.5 * binary.Volatility * binary.Volatility) * binary.Maturity) / volT,
                (Math.Log(underlyingSpotPrice / binary.StrikePrice) +
                 (-binary.BasisDiscount + 0.5 * binary.Volatility * binary.Volatility) * binary.Maturity) / volT -
                volT,
                -binary.BasisDiscount - binary.RiskFreeRate
            ),
            _ => (double.NaN, double.NaN, double.NaN)
        };
    }
}