using System.ComponentModel.DataAnnotations;
using System.Net.Sockets;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Core.Pricers;

public class FusedAccumulatorPricer : IPricing<FusedAccumulatorOption>
{
    public DerivativeResult CalculatePrice(FusedAccumulatorOption option, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        double comboPrice = 0, comboDelta = 0, comboGamma = 0, comboVega = 0, comboTheta = 0, comboRho = 0;
        double price, delta, gamma, vega, theta, rho;
        Func<FusedAccumulatorOption, double, int, CalculationMode, (double price, double delta, double gamma, double
            vega, double theta, double rho)> calculator = option.ContractType switch
        {
            ContractType.Call => FusedCall,
            ContractType.Put => FusedPut,
            _ => throw new ValidationException("Invalid option type")
        };
        foreach (var day in FacMaturity(option))
        {
            (price, delta, gamma, vega, theta, rho) = calculator(option, underlyingSpotPrice, day, mode);
            comboPrice += price;
            comboDelta += delta;
            comboGamma += gamma;
            comboVega += vega;
            comboTheta += theta;
            comboRho += rho;
        }

        var barrierPricer = new BarrierPricer();
        if (option.ContractType == ContractType.Call)
        {
            var barrier = GenBarrier(option, option.LowerBoundary, option.UpperBoundary, 0, option.DaysToExpiration,
                ContractType.Put, KnockType.UpOut);
            barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(-option.ExpiryGear,
                ref comboPrice, ref comboDelta, ref comboGamma, ref comboVega, ref comboTheta, ref comboRho);
        }
        else
        {
            var barrier = GenBarrier(option, option.UpperBoundary, option.LowerBoundary, 0, option.DaysToExpiration,
                ContractType.Call, KnockType.DownOut);
            barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(-option.ExpiryGear,
                ref comboPrice, ref comboDelta, ref comboGamma, ref comboVega, ref comboTheta, ref comboRho);
        }
        
        return new DerivativeResult()
        {
            BusiNo = option.BusiNo,
            Value = comboPrice,
            Delta = [comboDelta],
            Gamma = [comboGamma],
            Vega = [comboVega],
            Theta = comboTheta,
            Rho = comboRho,
            Volatility = [option.Volatility],
            BasisDiscount = [option.BasisDiscount],
            UnderlyingSpotPrice = [underlyingSpotPrice]
        };
    }

    public DerivativeResult CalculatePrice(FusedAccumulatorOption option, double[] underlyingSpotPrices,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        throw new ValidationException("FusedAccumulatorPricer does not support array of underlying prices");
    }

    private (double, double, double, double, double, double) FusedCall(FusedAccumulatorOption fac,
        double underlyingSpotPrice, int daysToExpiration, CalculationMode mode = CalculationMode.ValueOnly)
    {
        var barrierPricer = new BarrierPricer();
        var binaryBarrierPricer = new BinaryBarrierPricer();
        double upper = fac.UpperBoundary, lower = fac.LowerBoundary;
        double upperGear = fac.UpperGear, lowerGear = fac.LowerGear, middleGear = fac.MiddleGear;
        var fixRangeRebate = fac.FixedRangeRebate;
        double price = 0, delta = 0, gamma = 0, vega = 0, theta = 0, rho = 0;

        var barrier = GenBarrier(fac, lower, upper, 0, daysToExpiration, ContractType.Put, KnockType.UpOut);
        barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode)
            .AddResult(-lowerGear, ref price, ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
        var result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif
        if (fixRangeRebate != 0)
        {
            var binaryBarrier = GenBinaryBarrier(fac, lower, upper, 0, daysToExpiration, ContractType.Call,
                KnockType.UpOut);
            binaryBarrierPricer.CalculatePrice(binaryBarrier, underlyingSpotPrice, mode)
                .AddResult(middleGear, ref price, ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
            result = binaryBarrierPricer.CalculatePrice(binaryBarrier, underlyingSpotPrice, mode);
#endif
        }
        else
        {
            barrier = GenBarrier(fac, lower, upper, 0, daysToExpiration, ContractType.Call, KnockType.UpOut);
            barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode)
                .AddResult(middleGear, ref price, ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
            result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif
        }

        barrier = GenBarrier(fac, upper, upper, fac.Rebate, daysToExpiration, ContractType.Call, KnockType.UpOut);
        barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode)
            .AddResult(upperGear, ref price, ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
        result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif

        if (fac.PaymentType is AccumulatorPaymentType.FusedWithEnhance)
        {
            if (underlyingSpotPrice < upper)
            {
                barrier = GenBarrier(fac, upper, upper, 0, daysToExpiration, ContractType.Call, KnockType.UpIn);
                barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(upperGear, ref price,
                    ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
                result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif
                barrier = GenBarrier(fac, upper, upper, 0, daysToExpiration, ContractType.Put, KnockType.UpIn);
                barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(-upperGear, ref price,
                    ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
                result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif
            }
            else
            {
                barrier = GenBarrier(fac, upper, upper, underlyingSpotPrice - upper, daysToExpiration,
                    ContractType.Call, KnockType.UpOut);
                barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode)
                    .AddResult(upperGear, ref price, ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
                result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif
            }
        }
        
        return (price, delta, gamma, vega, theta, rho);
    }


    private (double, double, double, double, double, double) FusedPut(FusedAccumulatorOption fac,
        double underlyingSpotPrice
        , int daysToExpiration, CalculationMode mode = CalculationMode.ValueOnly)
    {
        var barrierPricer = new BarrierPricer();
        var binaryBarrierPricer = new BinaryBarrierPricer();
        double upper = fac.UpperBoundary, lower = fac.LowerBoundary;
        double upperGear = fac.UpperGear, lowerGear = fac.LowerGear, middleGear = fac.MiddleGear;
        var fixRangeRebate = fac.FixedRangeRebate;
        double price = 0, delta = 0, gamma = 0, vega = 0, theta = 0, rho = 0;

        var barrier = GenBarrier(fac, upper, lower, 0, daysToExpiration, ContractType.Call, KnockType.DownOut);
        barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(-upperGear, ref price, ref delta,
            ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
        var result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif
        if (fixRangeRebate != 0)
        {
            var binaryBarrier = GenBinaryBarrier(fac, upper, lower, 0, daysToExpiration, ContractType.Put,
                KnockType.DownOut);
            binaryBarrierPricer.CalculatePrice(binaryBarrier, underlyingSpotPrice, mode).AddResult(middleGear,
                ref price, ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
            result = binaryBarrierPricer.CalculatePrice(binaryBarrier, underlyingSpotPrice, mode);
#endif
        }
        else
        {
            barrier = GenBarrier(fac, upper, lower, 0, daysToExpiration, ContractType.Put, KnockType.DownOut);
            barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(middleGear, ref price,
                ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
            result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif
        }

        barrier = GenBarrier(fac, lower, lower, fac.Rebate, daysToExpiration, ContractType.Put, KnockType.DownOut);
        barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(lowerGear, ref price, ref delta,
            ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
        result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif
        if (fac.PaymentType is AccumulatorPaymentType.FusedWithEnhance)
        {
            if (underlyingSpotPrice > lower)
            {
                barrier = GenBarrier(fac, lower, lower, 0, daysToExpiration, ContractType.Call, KnockType.DownIn);
                barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(-lowerGear, ref price,
                    ref delta, ref gamma, ref vega, ref theta, ref rho);
                barrier = GenBarrier(fac, lower, lower, 0, daysToExpiration, ContractType.Put, KnockType.DownIn);
                barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(lowerGear, ref price,
                    ref delta, ref gamma, ref vega, ref theta, ref rho);
#if DEBUG
                result = barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode);
#endif
            }
            else
            {
                barrier = GenBarrier(fac, lower, lower, lower - underlyingSpotPrice, daysToExpiration, ContractType.Put,
                    KnockType.DownOut);
                barrierPricer.CalculatePrice(barrier, underlyingSpotPrice, mode).AddResult(lowerGear, ref price,
                    ref delta, ref gamma, ref vega, ref theta, ref rho);
            }
           
        }

        return (price, delta, gamma, vega, theta, rho);
    }

    private IEnumerable<int> FacMaturity(FusedAccumulatorOption fac)
    {
        for (int days = 0; days <= fac.DaysToExpiration; days++) // excluding last day
        {
            yield return days;
        }
    }

    private BarrierOption GenBarrier(FusedAccumulatorOption fac, double strike, double barrierLevel,
        double rebate, int days, ContractType contractType, KnockType knockType)
    {
        return new BarrierOption
        {
            KnockType = knockType,
            BarrierLevel = barrierLevel,
            KnockStatus = false,
            Rebate = rebate,
            ObservationFrequency = Frequency.Daily,
            RiskFreeRate = fac.RiskFreeRate,
            DividendYield = fac.DividendYield,
            DaysToExpiration = days,
            AnnualDenominator = fac.AnnualDenominator,
            ParticipationRate = 1.0,
            BasisDiscount = fac.BasisDiscount,
            UnderlyingType = fac.UnderlyingType,
            StrikePrice = strike,
            Volatility = fac.Volatility,
            IntraDayFraction = fac.IntraDayFraction,
            ContractType = contractType,
            ExerciseType = fac.ExerciseType,
            OptionType = OptionType.Barrier,
            PricingMethod = fac.PricingMethod,
        };
    }

    private BinaryBarrierOption GenBinaryBarrier(FusedAccumulatorOption fac, double strike, double barrierLevel,
        double rebate, int days, ContractType contractType, KnockType knockType)
    {
        return new BinaryBarrierOption
        {
            KnockType = knockType,
            BarrierLevel = barrierLevel,
            KnockStatus = false,
            Rebate = rebate,
            FixedPayment = fac.FixedRangeRebate,
            ObservationFrequency = Frequency.Daily,
            RiskFreeRate = fac.RiskFreeRate,
            DividendYield = fac.DividendYield,
            DaysToExpiration = days,
            OriginalDaysLeft = 0,
            AnnualDenominator = fac.AnnualDenominator,
            ParticipationRate = 1.0,
            BasisDiscount = fac.BasisDiscount,
            UnderlyingType = fac.UnderlyingType,
            StrikePrice = strike,
            Volatility = fac.Volatility,
            IntraDayFraction = fac.IntraDayFraction,
            ContractType = contractType,
            BinaryType = BinaryOptionType.CashOrNothing,
            ExerciseType = fac.ExerciseType,
            OptionType = OptionType.BinaryBarrier,
            AnnualizedFixedPayment = false,
            PricingMethod = fac.PricingMethod,
            DayCount = DayCountConvention.NA,
            PriceForSettlement = double.NaN,
        };
    }
}