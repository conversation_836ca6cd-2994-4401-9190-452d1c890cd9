using System.ComponentModel.DataAnnotations;
using System.Runtime.CompilerServices;
using DerivativePricing.Core.Internals;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using Microsoft.VisualBasic.CompilerServices;
using Utils = DerivativePricing.Core.Internals.Utils;

namespace DerivativePricing.Core.Pricers;

public class SnowballPricer : IPricing<SnowballOption>
{
    private readonly object _lock = new object();

    public DerivativeResult CalculatePrice(SnowballOption option, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        double price, delta = double.NaN, gamma = double.NaN, vega = double.NaN, theta = double.NaN, rho = double.NaN;
        var (mu, steps, dt, sigma, parallelOptions) = PathSimulator.McFactor(option);
        var simPaths = PathSimulator.Simulate(underlyingSpotPrice, mu, sigma, dt, option.PathNums, steps);
        price = McPrice(option, option.StrikePrice, simPaths, parallelOptions);
        if (mode != CalculationMode.ValueOnly)
        {
            Func<SnowballOption, double, DerivativeResult> calc = (opt, spot) =>
                CalculatePrice(opt, spot, CalculationMode.ValueOnly);
            (delta, gamma) = Utils.FiniteDiffDeltaGamma(option, price, underlyingSpotPrice, calc);
            vega = Utils.FiniteDiffVega(option, underlyingSpotPrice, calc);
            theta = FiniteDiffThetaDaily(option, price * option.ParticipationRate, underlyingSpotPrice);
            if (mode == CalculationMode.AllGreeks)
                rho = Utils.FiniteDiffRho(option, underlyingSpotPrice, calc);
        }

        return new DerivativeResult
        {
            BusiNo = option.BusiNo,
            Value = price * option.ParticipationRate,
            Delta = [delta * option.ParticipationRate],
            Gamma = [gamma * option.ParticipationRate],
            Vega = [vega * option.ParticipationRate],
            Theta = theta * option.ParticipationRate,
            Rho = rho * option.ParticipationRate,
            Volatility = [option.Volatility],
            UnderlyingSpotPrice = [underlyingSpotPrice],
            BasisDiscount = [option.BasisDiscount]
        };
    }

    public DerivativeResult CalculatePrice(SnowballOption option, double[] underlyingSpotPrices,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        throw new ValidationException("Snowball option does not support multi-asset pricing");
    }


    private double FiniteDiffThetaDaily(SnowballOption snow, double optionValue, double underlyingSpotPrice,
        int timeBump = 1)
    {
        if (snow.DaysToExpiration <= timeBump)
            return 0.0;
        var tradeDaySequence = snow.KoTradeDaySequence.ToArray();
        var koBarriers = snow.KoBarriers.ToArray();
        var coupons = snow.Coupons.ToArray();
        var fixedCalendarSequence = snow.KoFixedCalendarSequence.ToArray();

        for (var i = 0; i < tradeDaySequence.Length; i++)
        {
            tradeDaySequence[i] -= timeBump;
        }

        var startIndex = 0;
        if (tradeDaySequence[0] < 0)
        {
            startIndex = 1;
        }

        var newSnow = snow with
        {
            KoTradeDaySequence = tradeDaySequence[startIndex..],
            KoBarriers = koBarriers[startIndex..],
            Coupons = coupons[startIndex..],
            KoFixedCalendarSequence = fixedCalendarSequence[startIndex..],
            DaysToExpiration = snow.DaysToExpiration - timeBump
        };
        var nextValue = CalculatePrice(newSnow, underlyingSpotPrice, CalculationMode.ValueOnly).Value;
        var theta = nextValue - optionValue;
        return theta;
    }

    private double McPriceTest(SnowballOption snow, double notional, double[] simPaths, ParallelOptions parallelOptions)
    {
        var koCalendarFixedFractions = new double[snow.KoFixedCalendarSequence.Length];
        for (var i = 0; i < snow.KoFixedCalendarSequence.Length; i++)
        {
            koCalendarFixedFractions[i] = snow.KoFixedCalendarSequence[i] / 365.0;
        }

        var effectiveDiscounts = new double[snow.KoTradeDaySequence.Length];
        for (var i = 0; i < snow.KoTradeDaySequence.Length; i++)
        {
            effectiveDiscounts[i] = Math.Exp(-snow.RiskFreeRate * (snow.KoTradeDaySequence[i] + snow.CouponDateOffset) /
                                             snow.AnnualDenominator);
        }

        var calendarDayExpiryFraction = koCalendarFixedFractions[^1];
        var advanceExpiryPayoff = notional * snow.AdvancePaymentRate * snow.SwapRate * calendarDayExpiryFraction;
        var noKoKiPayoff = notional * snow.NoKoKiCoupon * calendarDayExpiryFraction * effectiveDiscounts[^1];
        var couponDateOffsetFraction = (double)snow.CouponDateOffset / snow.AnnualDenominator;
        var kiBarriers = new double[snow.DaysToExpiration + 1];
        var kiPrice = snow.KiPricePercentage * snow.StrikePrice;
        Array.Fill(kiBarriers, kiPrice, snow.KiObservationStart, kiBarriers.Length - snow.KiObservationStart);
        var price = 0.0;
        var offset = koCalendarFixedFractions.Length - snow.KoTradeDaySequence.Length;
        var pathNums = snow.PathNums;
        var stepNums = snow.DaysToExpiration + 1;

        if (snow.KnockType == KnockType.UpOut)
        {
            for (var i = 0; i < pathNums; i++)
            {
                // if (i == 16)
                //     Console.WriteLine("here");
                var singlePath = simPaths.AsSpan(i * stepNums, stepNums);
                price += UpOutPayoff(snow, notional, koCalendarFixedFractions, couponDateOffsetFraction,
                    effectiveDiscounts, advanceExpiryPayoff, noKoKiPayoff, offset, kiBarriers, singlePath);
            }

            return price / pathNums;
        }

        for (var i = 0; i < pathNums; i++)
        {
            var singlePath = simPaths.AsSpan(i * stepNums, stepNums);
            price += DownOutPayoff(snow, notional, koCalendarFixedFractions, couponDateOffsetFraction,
                effectiveDiscounts, advanceExpiryPayoff, noKoKiPayoff, offset, kiBarriers, singlePath);
        }

        return price / pathNums;
    }


    private double McPrice(SnowballOption snow, double notional, double[] simPaths,
        ParallelOptions parallelOptions)
    {
        var koCalendarFixedFractions = new double[snow.KoFixedCalendarSequence.Length];
        for (var i = 0; i < snow.KoFixedCalendarSequence.Length; i++)
        {
            koCalendarFixedFractions[i] = snow.KoFixedCalendarSequence[i] / 365.0;
        }

        var effectiveDiscounts = new double[snow.KoTradeDaySequence.Length];
        for (var i = 0; i < snow.KoTradeDaySequence.Length; i++)
        {
            effectiveDiscounts[i] = Math.Exp(-snow.RiskFreeRate * (snow.KoTradeDaySequence[i] + snow.CouponDateOffset) /
                                             snow.AnnualDenominator);
        }


        var calendarDayExpiryFraction = koCalendarFixedFractions[^1];
        var advanceExpiryPayoff = notional * snow.AdvancePaymentRate * snow.SwapRate * calendarDayExpiryFraction;
        var noKoKiPayoff = notional * snow.NoKoKiCoupon * calendarDayExpiryFraction * effectiveDiscounts[^1];
        var couponDateOffsetFraction = (double)snow.CouponDateOffset / snow.AnnualDenominator;
        var kiBarriers = new double[snow.DaysToExpiration + 1];
        var kiPrice = snow.KiPricePercentage * snow.StrikePrice;
        Array.Fill(kiBarriers, kiPrice, snow.KiObservationStart, kiBarriers.Length - snow.KiObservationStart);
        var price = 0.0;
        var offset = koCalendarFixedFractions.Length - snow.KoTradeDaySequence.Length;
        var pathNums = snow.PathNums;
        var stepNums = snow.DaysToExpiration + 1;

        if (snow.KnockType == KnockType.UpOut)
        {
            Parallel.For(0, pathNums, parallelOptions, () => 0.0, (i, state, local) =>
            {
                var singlePath = simPaths.AsSpan(i * stepNums, stepNums);
                local += UpOutPayoff(snow, notional, koCalendarFixedFractions, couponDateOffsetFraction,
                    effectiveDiscounts, advanceExpiryPayoff, noKoKiPayoff, offset, kiBarriers, singlePath);
                return local;
            }, local =>
            {
                lock (_lock)
                {
                    price += local;
                }
            });
            return price / pathNums;
        }

        Parallel.For(0, pathNums, parallelOptions, () => 0.0, (i, state, local) =>
        {
            var singlePath = simPaths.AsSpan(i * stepNums, stepNums);
            local += DownOutPayoff(snow, notional, koCalendarFixedFractions, couponDateOffsetFraction,
                effectiveDiscounts, advanceExpiryPayoff, noKoKiPayoff, offset, kiBarriers, singlePath);
            return local;
        }, local =>
        {
            lock (_lock)
            {
                price += local;
            }
        });
        return price / pathNums;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private double UpOutPayoff(
        SnowballOption snow,
        double notional,
        double[] koCalendarFractions,
        double couponDateOffsetFraction,
        double[] effectiveDiscounts,
        double advanceExpiryPayoff,
        double noKoKiPayoff,
        int offset,
        double[] kiBarriers,
        Span<double> singlePath)
    {
        var knockIdx = Utils.FirstOccurrenceGreaterThanOrEqual(singlePath, snow.KoBarriers, snow.KoTradeDaySequence);
        double payoff;
        double delayPayoff;
        if (knockIdx != -1)
        {
            var coupon = notional * snow.Coupons[knockIdx] * koCalendarFractions[knockIdx + offset];
            var swapPayoff = notional * snow.AdvancePaymentRate * snow.SwapRate *
                             koCalendarFractions[knockIdx + snow.CouponDateOffset];
            var callPayoff = snow.EnhanceSnowball
                ? (singlePath[snow.KoTradeDaySequence[knockIdx]] - snow.KoBarriers[knockIdx]) *
                  snow.EnhanceParticipationRate
                : 0.0;
            var total = coupon + swapPayoff + callPayoff;
            delayPayoff = total * snow.DelayPaymentRate * couponDateOffsetFraction;
            payoff = (total + delayPayoff) * effectiveDiscounts[knockIdx];
            return payoff;
        }

        // knock-in check
        knockIdx = Utils.FirstOccurrenceLessThan(singlePath, kiBarriers);
        if (knockIdx != -1 || snow.KiStatus)
        {
            var effectiveSpot = Math.Max(snow.KiFloorPercentage, singlePath[^1] / snow.StrikePrice);
            var putPayoff = -notional * Math.Max(1 - effectiveSpot, 0) * snow.ParticipationRate;
            var advPayoff = snow.PayWhenKi ? advanceExpiryPayoff : 0;
            var total = advPayoff + putPayoff;
            delayPayoff = total * snow.DelayPaymentRate * couponDateOffsetFraction;
            payoff = (total + delayPayoff) * effectiveDiscounts[^1];
            return payoff;
        }

        var totalNoKoKi = advanceExpiryPayoff + noKoKiPayoff;
        var delayNoKoKi = totalNoKoKi * snow.DelayPaymentRate * couponDateOffsetFraction;
        payoff = (totalNoKoKi + delayNoKoKi) * effectiveDiscounts[^1];
        return payoff;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private double DownOutPayoff(
        SnowballOption snow,
        double notional,
        double[] koCalendarFractions,
        double couponDateOffsetFraction,
        double[] effectiveDiscounts,
        double advanceExpiryPayoff,
        double noKoKiPayoff,
        int offset,
        double[] kiBarriers,
        Span<double> singlePath)
    {
        var knockIdx = Utils.FirstOccurrenceLessThanOrEqual(singlePath, snow.KoBarriers, snow.KoTradeDaySequence);
        double payoff;
        double delayPayoff;
        if (knockIdx != -1)
        {
            var coupon = notional * snow.Coupons[knockIdx] * koCalendarFractions[knockIdx + offset];
            var swapPayoff = notional * snow.AdvancePaymentRate * snow.SwapRate *
                             koCalendarFractions[knockIdx + snow.CouponDateOffset];
            var putPayoff = snow.EnhanceSnowball
                ? (snow.KoBarriers[knockIdx] - singlePath[snow.KoTradeDaySequence[knockIdx]]) *
                  snow.EnhanceParticipationRate
                : 0.0;
            var total = coupon + swapPayoff + putPayoff;
            delayPayoff = total * snow.DelayPaymentRate * couponDateOffsetFraction;
            payoff = (total + delayPayoff) * effectiveDiscounts[knockIdx];
            return payoff;
        }

        knockIdx = Utils.FirstOccurrenceGreaterThan(singlePath, kiBarriers);
        if (knockIdx != -1 || snow.KiStatus)
        {
            var effectiveSpot = Math.Min(snow.KiFloorPercentage, singlePath[^1] / snow.StrikePrice);
            var callPayoff = -notional * Math.Max(effectiveSpot - 1, 0) * snow.ParticipationRate;
            var advPayoff = snow.PayWhenKi ? advanceExpiryPayoff : 0;
            var total = advPayoff + callPayoff;
            delayPayoff = total * snow.DelayPaymentRate * couponDateOffsetFraction;
            payoff = (total + delayPayoff) * effectiveDiscounts[^1];
            return payoff;
        }

        var totalNoKoKi = advanceExpiryPayoff + noKoKiPayoff;
        var delayNoKoKi = totalNoKoKi * snow.DelayPaymentRate * couponDateOffsetFraction;
        return (totalNoKoKi + delayNoKoKi) * effectiveDiscounts[^1];
    }
}