using System.ComponentModel.DataAnnotations;
using DerivativePricing.Core.Internals;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Domain.Models;
using FinEnumerations;
using MathNet.Numerics.Distributions;

namespace DerivativePricing.Core.Pricers;

public class SpreadPricer : IPricing<SpreadOption>
{
    public DerivativeResult CalculatePrice(SpreadOption option, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        throw new ValidationException("SpreadOption does not support single-asset pricing");
    }

    public DerivativeResult CalculatePrice(SpreadOption option, double[] underlyingSpotPrices,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        if (underlyingSpotPrices.Length != 2)
            throw new ValidationException("SpreadOption requires two underlying prices");

        var b1 = Utils.CarryCost(option);
        var dummyOpt = option with { UnderlyingType = option.UnderlyingType2, BasisDiscount = option.BasisDiscount2 };
        var b2 = Utils.CarryCost(dummyOpt);

        var price = SpreadApproximation(option.ContractType, underlyingSpotPrices[0], underlyingSpotPrices[1],
            option.Volume, option.Volume2, option.StrikePrice, option.Maturity,
            option.RiskFreeRate, b1, b2, option.Volatility, option.Volatility2, option.Rho);

        double[] deltas, gammas, vegas;
        double theta = double.NaN, rho = double.NaN, crossGamma = double.NaN;

        if (mode != CalculationMode.ValueOnly)
        {
            (deltas, gammas) = FiniteDiffDeltasGammas(option, price, underlyingSpotPrices);
            vegas = FiniteDiffVega(option, underlyingSpotPrices);
            theta = FiniteDiffTheta(option, underlyingSpotPrices);

            if (mode == CalculationMode.AllGreeks)
            {
                rho = FiniteDiffRho(option, underlyingSpotPrices);
                crossGamma = CrossGamma(option, underlyingSpotPrices, 0.001, 0.001);
            }
        }
        else
        {
            deltas = [double.NaN, double.NaN];
            gammas = [double.NaN, double.NaN];
            vegas = [double.NaN, double.NaN];
        }

        return new DerivativeResult
        {
            BusiNo = option.BusiNo,
            Value = price,
            Delta = deltas,
            Gamma = [gammas[0], gammas[1], crossGamma],
            Vega = vegas,
            Theta = theta,
            Rho = rho,
            Volatility = [option.Volatility, option.Volatility2],
            BasisDiscount = [option.BasisDiscount, option.BasisDiscount2],
            UnderlyingSpotPrice = underlyingSpotPrices
        };
    }

    private double SpreadApproximation(
        ContractType callPutFlag, double S1, double S2, double Q1, double Q2, double X, double T,
        double r, double b1, double b2, double v1, double v2, double rho)
    {
        var expB2RT = Math.Exp((b2 - r) * T);
        var expNegRT = Math.Exp(-r * T);

        var Q2S2expB2RT = Q2 * S2 * expB2RT;
        var denom = Q2S2expB2RT + X * expNegRT;
        var F = Q2S2expB2RT / denom;

        var v2F = v2 * F;
        var v = Math.Sqrt(v1 * v1 + v2F * v2F - 2.0 * rho * v1 * v2F);

        var S = (Q1 * S1 * Math.Exp((b1 - r) * T)) / denom;

        var logS = Math.Log(S);
        var vSqrT = v * Math.Sqrt(T);
        var d1 = (logS + 0.5 * v * v * T) / vSqrT;
        var d2 = d1 - vSqrT;

        if (callPutFlag == ContractType.Call)
        {
            return denom * (S * Normal.CDF(0, 1, d1) - Normal.CDF(0, 1, d2));
        }

        return denom * (Normal.CDF(0, 1, -d2) - S * Normal.CDF(0, 1, -d1));
    }

    private (double[], double[]) FiniteDiffDeltasGammas(SpreadOption spread, double optValue,
        double[] spots, double priceBump = 0.0001)
    {
        var realBump1 = spots[0] * priceBump;
        var realBump2 = spots[1] * priceBump;
        double[] upSpots = [spots[0] + realBump1, spots[1]];
        var upValue = CalculatePrice(spread, upSpots).Value;
        double[] downSpots = [spots[0] - realBump1, spots[1]];
        var downValue = CalculatePrice(spread, downSpots).Value;
        var delta1 = (upValue - downValue) / (2 * realBump1);
        var gamma1 = (upValue - 2 * optValue + downValue) / (realBump1 * realBump1);
        upSpots = [spots[0], spots[1] + realBump2];
        upValue = CalculatePrice(spread, upSpots).Value;
        downSpots = [spots[0], spots[1] - realBump2];
        downValue = CalculatePrice(spread, downSpots).Value;
        var delta2 = (upValue - downValue) / (2 * realBump2);
        var gamma2 = (upValue - 2 * optValue + downValue) / (realBump2 * realBump2);
        return ([delta1, delta2], [gamma1, gamma2]);
    }

    private double CrossGamma(SpreadOption spread, double[] spots, double priceBump1, double priceBump2)
    {
        var realBump1 = spots[0] * priceBump1;
        var realBump2 = spots[1] * priceBump2;
        double[] upSpots = [spots[0] + realBump1, spots[1] + realBump2];
        var upValue = CalculatePrice(spread, upSpots).Value;

        double[] plusMinusSpots = [spots[0] + realBump1, spots[1] - realBump2];
        var plusMinusValue = CalculatePrice(spread, plusMinusSpots).Value;

        double[] minusPlusSpots = [spots[0] - realBump1, spots[1] + realBump2];
        var minusPlusValue = CalculatePrice(spread, minusPlusSpots).Value;

        double[] downSpots = [spots[0] - realBump1, spots[1] - realBump2];
        var downValue = CalculatePrice(spread, downSpots).Value;
        var crossGamma = (upValue - plusMinusValue - minusPlusValue + downValue) /
                         (4 * realBump1 * realBump2);
        return crossGamma;
    }

    private double[] FiniteDiffVega(SpreadOption spread, double[] spots,
        double volBump = 0.01)
    {
        var upSpread = spread with { Volatility = spread.Volatility + volBump };
        var downSpread = spread with { Volatility = spread.Volatility - volBump };
        var upValue = CalculatePrice(upSpread, spots).Value;
        var downValue = CalculatePrice(downSpread, spots).Value;
        var vega1 = (upValue - downValue) / (2 * volBump);
        var upSpread2 = spread with { Volatility2 = spread.Volatility2 + volBump };
        var downSpread2 = spread with { Volatility2 = spread.Volatility2 - volBump };
        var upValue2 = CalculatePrice(upSpread2, spots).Value;
        var downValue2 = CalculatePrice(downSpread2, spots).Value;
        var vega2 = (upValue2 - downValue2) / (2 * volBump);
        return [vega1, vega2];
    }

    private double FiniteDiffTheta(SpreadOption spread, double[] spots, double timeBump = 0.001)
    {
        var upSpread = spread with { IntraDayFraction = spread.IntraDayFraction - timeBump * spread.Maturity };
        var downSpread = spread with { IntraDayFraction = spread.IntraDayFraction + timeBump * spread.Maturity };
        var upValue = CalculatePrice(upSpread, spots).Value;
        var downValue = CalculatePrice(downSpread, spots).Value;
        var theta = (upValue - downValue) / (2 * timeBump * spread.Maturity);
        return theta / spread.AnnualDenominator;
    }

    private double FiniteDiffRho(SpreadOption spread, double[] spots,
        double rhoBump = 0.01)
    {
        var upSpread = spread with { RiskFreeRate = spread.RiskFreeRate + rhoBump };
        var downSpread = spread with { RiskFreeRate = spread.RiskFreeRate - rhoBump };
        var upValue = CalculatePrice(upSpread, spots).Value;
        var downValue = CalculatePrice(downSpread, spots).Value;
        var rho = (upValue - downValue) / (2 * rhoBump);
        return rho * 0.01;
    }
}