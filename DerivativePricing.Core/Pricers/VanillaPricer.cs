using System.ComponentModel.DataAnnotations;
using System.ComponentModel.Design.Serialization;
using System.Net.Sockets;
using DerivativePricing.Domain.Models;
using DerivativePricing.Domain.Interfaces;
using DerivativePricing.Core.Internals;
using FinEnumerations;
using MathNet.Numerics.Distributions;


namespace DerivativePricing.Core.Pricers;

public partial class VanillaPricer : IPricing<VanillaOption>
{
    public DerivativeResult CalculatePrice(VanillaOption option, double underlyingSpotPrice,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        double price, delta = double.NaN, gamma = double.NaN, vega = double.NaN, theta = double.NaN, rho = double.NaN;

        if (option.ExerciseType == ExerciseType.EuropeanExercise)
        {
            (price, delta, gamma, vega, theta, rho) = mode != CalculationMode.ValueOnly
                ? EuropeanGreeks(option, underlyingSpotPrice, mode)
                : (BsmPrice(option, underlyingSpotPrice).Item1, delta, gamma, vega, theta, rho);
        }
        else
        {
            (price, delta, gamma, vega, theta, rho) = mode != CalculationMode.ValueOnly
                ? AmericanGreeks(option, underlyingSpotPrice, mode)
                : (BSAmericanApprox2002(option, underlyingSpotPrice), delta, gamma, vega, theta, rho);
        }

        return new DerivativeResult
        {
            BusiNo = option.BusiNo,
            Value = price * option.ParticipationRate,
            Delta = [delta * option.ParticipationRate],
            Gamma = [gamma * option.ParticipationRate],
            Vega = [vega * option.ParticipationRate],
            Theta = theta * option.ParticipationRate,
            Rho = rho * option.ParticipationRate,
            Volatility = [option.Volatility],
            BasisDiscount = [option.BasisDiscount],
            UnderlyingSpotPrice = [underlyingSpotPrice]
        };
    }

    public DerivativeResult CalculatePrice(VanillaOption option, double[] underlyingSpotPrices,
        CalculationMode mode = CalculationMode.ValueOnly)
    {
        throw new ValidationException($"Vanilla Option does not support multi-asset pricing");
    }

    private (double, double, double, double, double, double) EuropeanGreeks(VanillaOption option,
        double underlyingSpotPrice,
        CalculationMode mode)
    {
        var (price, d1, d2, br) = BsmPrice(option, underlyingSpotPrice);
        // var intrinsic = EuropeanPayoff(underlyingSpotPrice, option.StrikePrice, option.ContractType);
        // deep in the money with american exercise
        // if (intrinsic > price && option.ExerciseType == ExerciseType.AmericanExercise)
        //     price = intrinsic;
        var pdf = Math.Exp(-0.5 * d1 * d1) * 0.3989422804014327;
        var delta = Delta(d1, br, option);
        var gamma = Gamma(underlyingSpotPrice, br, pdf, option);
        var vega = Vega(underlyingSpotPrice, br, pdf, option.Maturity);
        var theta = Theta(underlyingSpotPrice, d1, d2, br, pdf, option);
        var rho = mode == CalculationMode.AllGreeks ? Rho(d2, price, option) : double.NaN;
        return (price, delta, gamma, vega, theta, rho);
    }

    private (double, double, double, double, double, double) AmericanGreeks(VanillaOption option,
        double underlyingSpotPrice,
        CalculationMode mode)
    {
        var price = BSAmericanApprox2002(option, underlyingSpotPrice);
        Func<VanillaOption, double, DerivativeResult> calc = (opt, spot) =>
            CalculatePrice(opt, spot, CalculationMode.ValueOnly);
        var (delta, gamma) =
            Utils.FiniteDiffDeltaGamma<VanillaOption>(option, price, underlyingSpotPrice, calc);
        var vega = Utils.FiniteDiffVega<VanillaOption>(option, underlyingSpotPrice, calc);
        var theta = Utils.FiniteDiffTheta<VanillaOption>(option, underlyingSpotPrice, calc);
        var rho = mode == CalculationMode.AllGreeks
            ? Utils.FiniteDiffRho(option, underlyingSpotPrice, calc)
            : double.NaN;
        return (price, delta, gamma, vega, theta, rho);
    }


    internal double EuropeanPayoff(double underlyingSpotPrice, double strikePrice, ContractType contractType)
    {
        return contractType switch
        {
            ContractType.Call => Math.Max(underlyingSpotPrice - strikePrice, 0),
            ContractType.Put => Math.Max(strikePrice - underlyingSpotPrice, 0),
            _ => throw new ArgumentOutOfRangeException(nameof(contractType), contractType,
                "Chooser Option not implemented")
        };
    }

    internal (double, double, double, double) BsmPrice(VanillaOption option, double underlyingSpotPrice)
    {
        if (option.OptionType != OptionType.Vanilla)
            throw new ArgumentException("Option type must be vanilla");
        if (option.Maturity == 0)
            return (EuropeanPayoff(underlyingSpotPrice, option.StrikePrice, option.ContractType), 0.0, 0.0, 0.0);
        var cost = Utils.CarryCost(option);
        var volT = Math.Sqrt(option.Maturity) * option.Volatility;
        var b = cost;
        var br = b - option.RiskFreeRate;
        var temp = Math.Log(underlyingSpotPrice / option.StrikePrice) +
                   (b + 0.5 * option.Volatility * option.Volatility) * option.Maturity;
        var d1 = temp / volT;
        var d2 = d1 - volT;
        var discount = Math.Exp(-option.RiskFreeRate * option.Maturity);
        var npv = option.ContractType switch
        {
            ContractType.Call => underlyingSpotPrice * Math.Exp(br * option.Maturity) * Normal.CDF(0, 1, d1) -
                                 option.StrikePrice * discount * Normal.CDF(0, 1, d2),
            ContractType.Put => option.StrikePrice * discount * Normal.CDF(0, 1, -d2) -
                                underlyingSpotPrice * Math.Exp(br * option.Maturity) * Normal.CDF(0, 1, -d1),
            _ => throw new ArgumentOutOfRangeException($"Chooser Option not implemented")
        };
        return (npv, d1, d2, br);
    }


    private double Delta(double d1, double br, VanillaOption option)
    {
        if (option.Maturity < 1.0 / (243 * 6 * 60))
            return option.ContractType switch
            {
                ContractType.Call => 1,
                ContractType.Put => -1,
                _ => throw new ArgumentOutOfRangeException($"Chooser Option not implemented")
            };
        return option.ContractType switch
        {
            ContractType.Call => Math.Exp(br * option.Maturity) * Normal.CDF(0, 1, d1),
            ContractType.Put => Math.Exp(br * option.Maturity) * (Normal.CDF(0, 1, d1) - 1),
            _ => throw new ArgumentOutOfRangeException($"Chooser Option not implemented")
        };
    }

    private double Gamma(double underlyingSpotPrice, double br, double pdf, VanillaOption option)
    {
        if (option.Maturity == 0)
            return 0.0;
        var gamma = pdf * Math.Exp(br * option.Maturity) /
                    (underlyingSpotPrice * option.Volatility * Math.Sqrt(option.Maturity));
        return gamma;
    }

    private double Vega(double underlyingSpotPrice, double br, double pdf, double maturity)
    {
        var vega = underlyingSpotPrice * Math.Exp(br * maturity) * pdf * Math.Sqrt(maturity);
        return vega * 0.01; // 1 pct change in volatility
    }

    private double Theta(double underlyingSpotPrice, double d1, double d2, double br, double pdf,
        VanillaOption option)
    {
        if (option.Maturity == 0)
            return 0.0;
        var discount = Math.Exp(-option.RiskFreeRate * option.Maturity);
        var theta = option.ContractType switch
        {
            ContractType.Call => -0.5 * underlyingSpotPrice * pdf * Math.Exp(br * option.Maturity) * option.Volatility /
                                 Math.Sqrt(option.Maturity)
                                 - br * underlyingSpotPrice * Math.Exp(br * option.Maturity) * Normal.CDF(0, 1, d1)
                                 - option.RiskFreeRate * discount * option.StrikePrice * Normal.CDF(0, 1, d2),
            ContractType.Put => -0.5 * underlyingSpotPrice * pdf * Math.Exp(br * option.Maturity) * option.Volatility /
                                Math.Sqrt(option.Maturity)
                                + br * underlyingSpotPrice * Math.Exp(br * option.Maturity) * Normal.CDF(0, 1, -d1)
                                + option.RiskFreeRate * discount * option.StrikePrice * Normal.CDF(0, 1, -d2),
            _ => throw new ArgumentOutOfRangeException($"Chooser Option not implemented")
        };
        return theta / (double)option.AnnualDenominator; // 1 day theta
    }

    private double Rho(double d2, double optionValue, VanillaOption option)
    {
        var discount = Math.Exp(-option.RiskFreeRate * option.Maturity);
        double rho;
        // https://riskencyclopedia.com/articles/black_1976/
        if (option.UnderlyingType == SecType.CommodityFutures)
        {
            rho = -option.Maturity * optionValue;
        }
        else
        {
            rho = option.ContractType switch
            {
                ContractType.Call => option.StrikePrice * option.Maturity * discount * Normal.CDF(0, 1, d2),
                ContractType.Put => -option.StrikePrice * option.Maturity * discount * Normal.CDF(0, 1, -d2),
                _ => throw new ArgumentOutOfRangeException($"Chooser Option not implemented")
            };
        }

        return rho * 0.01;
    }
}