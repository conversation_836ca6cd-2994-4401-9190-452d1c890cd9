using DerivativePricing.Domain.Models;
using FinEnumerations;


namespace DerivativePricing.Tests.Accumulator;

public class AccumulatorFactor
{
    public static AccumulatorOption CreateAccumulator()
    {
        var accumulator = new AccumulatorOption
        {
            StrikePrices =  [2893, 2893],
            BarrierLevels = [2627,999999],
            Gearing = [1, -2.0],
            Rebates =  [0, 0],
            KnockTypes = [KnockType.DownOut, KnockType.UpOut],
            ContractTypes = [ContractType.Put, ContractType.Call],
            DaysToExpiration = 71,
            InitialAccumulatingDays = 78,
            RiskFreeRate = 0.03,
            DividendYield = 0,
            ParticipationRate = 1,
            BasisDiscount = 0,
            UnderlyingType = SecType.CommodityFutures,
            Volatility = 0.25,
            IntraDayFraction = 0,
            ContractType = ContractType.Put,
            ExerciseType = ExerciseType.EuropeanExercise,
            OptionType = OptionType.Accumulator,
            PricingMethod = PricingMethod.Analytic,
            AnnualDenominator = 243,
            ExpiryObservationFactor = null,
        };
        return accumulator;
    }
}