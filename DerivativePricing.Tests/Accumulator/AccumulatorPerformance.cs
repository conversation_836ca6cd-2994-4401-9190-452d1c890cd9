using BenchmarkDotNet.Attributes;
using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.Accumulator;

[MemoryDiagnoser]
public class AccumulatorPerformanceTest
{
    private double _spotPrice;

    [GlobalSetup]
    public void Setup()
    {
        _spotPrice = 100.0;
    }

    [Benchmark]
    public DerivativeResult PriceAccumulatorOption()
    {
        var accumulator = AccumulatorFactor.CreateAccumulator();
        var accumulatorPricer = new AccumulatorPricer();
        return accumulatorPricer.CalculatePrice(accumulator, _spotPrice);
    }

    [Benchmark]
    public DerivativeResult CalculateAccumulatorGreeks()
    {
        var accumulator = AccumulatorFactor.CreateAccumulator();
        var accumulatorPricer = new AccumulatorPricer();
        return accumulatorPricer.CalculatePrice(accumulator, _spotPrice, CalculationMode.AllGreeks);
    }
}