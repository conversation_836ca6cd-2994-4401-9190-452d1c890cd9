using DerivativePricing.Core.Pricers;
using FinEnumerations;

namespace DerivativePricing.Tests.Accumulator;

public class AccumulatorTest
{
    public void TestAccumulatorPricing()
    {
        var spot = 2895;
        var accumulator = AccumulatorFactor.CreateAccumulator();
        var accumulatorPricer = new AccumulatorPricer();
        // var price = AccumulatorPricer.CalculatePrice(accumulator, spot);
        // Utils.PrintResult(price);
        var accumulatorGreeks = accumulatorPricer.CalculatePrice(accumulator, spot,CalculationMode.AllGreeks);
        Utils.PrintResult(accumulatorGreeks);
    }
    
    
}