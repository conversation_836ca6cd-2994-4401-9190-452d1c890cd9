using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.Asian;

public class AsianFactor
{
    public static AsianOption CreateAsian()
    {
        return new AsianOption
        {
            RiskFreeRate = 0.03,
            DividendYield = 0,
            DaysToExpiration = 21,
            AnnualDenominator = 243,
            ParticipationRate = 1,
            BasisDiscount = 0,
            UnderlyingType = SecType.CommodityFutures,
            StrikePrice = 100,
            Volatility = 0.26,
            IntraDayFraction = 0,
            ContractType = ContractType.Put,
            ExerciseType = ExerciseType.EuropeanExercise,
            PricingMethod = PricingMethod.MonteCarlo,
            OptionType = OptionType.Asian,
            AverageType = AverageType.Arithmetic,
            ObservationFrequency = Frequency.Daily,
            FixingPrice = 100/ 22.0,
            DayCount = DayCountConvention.TradeDay,
            FixingPeriodLength = 22,
            EnhancePrice = 0,
            GuaranteedPayoff = 0,
            PathNums = 1 << 16
        };

    }
}