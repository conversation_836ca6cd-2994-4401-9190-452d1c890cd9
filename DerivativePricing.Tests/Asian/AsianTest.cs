using DerivativePricing.Core.Pricers;
using FinEnumerations;

namespace DerivativePricing.Tests.Asian;

public class AsianTest
{
    public void TestAsianPricing()
    {
        var spot = 100;
        var asian = AsianFactor.CreateAsian();
        var asianPricer = new AsianPricer();
        var asianGreeks = asianPricer.CalculatePrice(asian, spot, CalculationMode.AllGreeks);
        Utils.PrintResult(asianGreeks);
    }
}