using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.Barrier;

public static class BarrierFactor
{
    public static BarrierOption CreateBarrier()
    {
        var barrier = new BarrierOption
        {
            StrikePrice = 85,
            BarrierLevel = 85,
            Volatility = 0.2,
            RiskFreeRate = 0.03,
            DividendYield = 0.0,
            BasisDiscount = -0.0,
            DaysToExpiration = 4,
            IntraDayFraction = 0.0 / 243,
            ContractType = ContractType.Call,
            PricingMethod = PricingMethod.Analytic,
            UnderlyingType = SecType.CommodityFutures,
            ExerciseType = ExerciseType.EuropeanExercise,
            OptionType = OptionType.Barrier,
            AnnualDenominator = 243,
            ParticipationRate = 1.0,
            KnockType = KnockType.DownIn,
            KnockStatus = false,
            Rebate = 0.0,
            ObservationFrequency = Frequency.Daily
        };
        return barrier;
    }
}