using BenchmarkDotNet.Attributes;
using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.Barrier;

[MemoryDiagnoser]
public class BarrierPerformance
{
    private double _spotPrice;

    [GlobalSetup]
    public void Setup()
    {
        _spotPrice = 100.0;
        
    }

    [Benchmark]
    public DerivativeResult PriceBarrierOption()
    {
        var barrierOption = BarrierFactor.CreateBarrier();
        var barrierPricer = new BarrierPricer();
        return barrierPricer.CalculatePrice(barrierOption, _spotPrice);
    }

    [Benchmark]
    public DerivativeResult CalculateBarrierGreeks()
    {
        var barrierOption = BarrierFactor.CreateBarrier();
        var barrierPricer = new BarrierPricer();
        return barrierPricer.CalculatePrice(barrierOption, _spotPrice, CalculationMode.AllGreeks);
    }
}