using DerivativePricing.Core.Pricers;
using FinEnumerations;

namespace DerivativePricing.Tests.Barrier;

public class BarrierTest
{
    public void TestBarrierPricing()
    {
        var spot = 85;
        var barrier = BarrierFactor.CreateBarrier();
        
        var barrierPricer = new BarrierPricer();
        // var result = BarrierPricer.CalculatePrice(barrier, spot);
        var result = barrierPricer.CalculatePrice(barrier, spot, CalculationMode.AllGreeks);
        // Console.WriteLine(result);
        Utils.PrintResult(result);
    }
}