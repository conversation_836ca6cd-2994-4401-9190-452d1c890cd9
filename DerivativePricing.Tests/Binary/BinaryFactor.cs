using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.Binary;

public static class BinaryFactor
{
    public static BinaryOption CreateFactor()
    {
        var binary = new BinaryOption
        {
            StrikePrice = 100,
            Volatility = 0.235,
            RiskFreeRate = 0.03,
            DividendYield = 0.0,
            BasisDiscount = -0.0,
            DaysToExpiration = 22,
            IntraDayFraction = 0,
            OriginalDaysLeft = 22,
            ContractType = ContractType.Call,
            PricingMethod = PricingMethod.Analytic,
            UnderlyingType = SecType.CommodityFutures,
            ExerciseType = ExerciseType.EuropeanExercise,
            OptionType = OptionType.Binary,
            ParticipationRate = 1,
            BinaryType = BinaryOptionType.CashOrNothing,
            AnnualizedFixedPayment = false,
            FixedPayment = 100.0,
            DayCount = DayCountConvention.NA,
            PriceForSettlement = 0,
            AnnualDenominator = 243,
        };
        return binary;
    }
}