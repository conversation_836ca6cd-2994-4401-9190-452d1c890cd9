using DerivativePricing.Domain.Models;
using DerivativePricing.Core.Pricers;
using FinEnumerations;

namespace DerivativePricing.Tests.Binary;

public class BinaryTest
{
    public void TestBinaryPricing()
    {
        var spot = 100;
        var binary = BinaryFactor.CreateFactor();
        var binaryPricer = new BinaryPricer();
        var binaryGreeks = binaryPricer.CalculatePrice(binary, spot, CalculationMode.AllGreeks);
        Utils.PrintResult(binaryGreeks);
    }
    
    
}