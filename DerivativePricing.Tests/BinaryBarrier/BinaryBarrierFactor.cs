using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.BinaryBarrier;

public class BinaryBarrierFactor
{
    public static BinaryBarrierOption CreateBinaryBarrier()
    {
        var binaryBarrier = new BinaryBarrierOption
        {
            StrikePrice = 100,
            BarrierLevel = 85,
            Volatility = 0.26,
            Rebate = 10,
            KnockStatus = false,
            RiskFreeRate = 0.03,
            DividendYield = 0,
            DaysToExpiration = 22,
            ParticipationRate = 1,
            BasisDiscount = 0,
            FixedPayment = 20,
            OriginalDaysLeft = 22,
            IntraDayFraction = 0,
            ObservationFrequency = Frequency.Daily,
            KnockType = KnockType.DownOut,
            ContractType = ContractType.Call,
            ExerciseType = ExerciseType.EuropeanExercise,
            PricingMethod = PricingMethod.Analytic,
            BinaryType = BinaryOptionType.CashOrNothing,
            UnderlyingType = SecType.CommodityFutures,
            OptionType = OptionType.BinaryBarrier,
            AnnualizedFixedPayment = false,
            DayCount = DayCountConvention.NA,
            PriceForSettlement = 0,
            AnnualDenominator = 243,
        };
        return binaryBarrier;
    }
}