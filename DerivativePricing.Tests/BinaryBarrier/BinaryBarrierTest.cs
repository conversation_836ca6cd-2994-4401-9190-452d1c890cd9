using DerivativePricing.Core.Pricers;
using FinEnumerations;

namespace DerivativePricing.Tests.BinaryBarrier;

public class BinaryBarrierTest
{
    public void TestBinaryBarrierPricing()
    {
        var spot = 100.0;
        var binaryBarrier = BinaryBarrierFactor.CreateBinaryBarrier();
        var binaryBarrierPricer = new BinaryBarrierPricer();
        var result = binaryBarrierPricer.CalculatePrice(binaryBarrier, spot, CalculationMode.AllGreeks);
        Utils.PrintResult(result);
    }
}