<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <PlatformTarget>x64</PlatformTarget>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <PlatformTarget>x64</PlatformTarget>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\DerivativePricing.Abstraction\DerivativePricing.Abstraction.csproj" />
      <ProjectReference Include="..\DerivativePricing.Core\DerivativePricing.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="BenchmarkDotNet" Version="0.15.0" />
      <PackageReference Include="Spectre.Console" Version="0.50.0" />
    </ItemGroup>

</Project>
