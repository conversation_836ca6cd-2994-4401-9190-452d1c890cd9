using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.FusedAccumulator;

public class FuseAccumulatorFactor
{
    public static FusedAccumulatorOption CreateFusedAccumulator()
    {
        return new FusedAccumulatorOption
        {
            LowerBoundary = 85,
            UpperBoundary = 115,
            LowerGear = 2,
            UpperGear = 1,
            MiddleGear = 1,
            ExpiryGear = 20.0,
            FixedRangeRebate = 0.0,
            Rebate = 15.0,
            PaymentType = AccumulatorPaymentType.FusedWithEnhance,
            RiskFreeRate = 0.03,
            DividendYield = 0,
            DaysToExpiration = 22,
            AnnualDenominator = 243,
            ParticipationRate = 1,
            BasisDiscount = 0,
            UnderlyingType = SecType.CommodityFutures,
            Volatility = 0.2,
            IntraDayFraction = 0,
            ContractType = ContractType.Put,
            ExerciseType = ExerciseType.EuropeanExercise,
            OptionType = OptionType.FusedAccumulator,
            PricingMethod = PricingMethod.Analytic
        };
    }
}