using BenchmarkDotNet.Attributes;
using DerivativePricing.Core.Pricers;
using FinEnumerations;

namespace DerivativePricing.Tests.FusedAccumulator;

[MemoryDiagnoser]
public class FusedAccumulatorPerformance
{
    private double _spotPrice;

    [GlobalSetup]
    public void Setup()
    {
        _spotPrice = 95.0;
    }

    [Benchmark]
    public DerivativePricing.Domain.Models.DerivativeResult PriceFusedAccumulatorOption()
    {
        var pricer = new FusedAccumulatorPricer();
        var option = FuseAccumulatorFactor.CreateFusedAccumulator();
        return pricer.CalculatePrice(option, _spotPrice);
    }

    [Benchmark]
    public DerivativePricing.Domain.Models.DerivativeResult CalculateFusedAccumulatorGreeks()
    {
        var pricer = new FusedAccumulatorPricer();
        var option = FuseAccumulatorFactor.CreateFusedAccumulator();
        return pricer.CalculatePrice(option, _spotPrice, CalculationMode.AllGreeks);
    }
}

