namespace DerivativePricing.Tests.FusedAccumulator;

public class FusedAccumulatorTest
{
    public void TestFusedAccumulatorPricing()
    {
        var spot = 100;
        var fusedAccumulator = FuseAccumulatorFactor.CreateFusedAccumulator();
        var fusedAccumulatorPricer = new DerivativePricing.Core.Pricers.FusedAccumulatorPricer();
        var fusedAccumulatorGreeks = fusedAccumulatorPricer.CalculatePrice(fusedAccumulator, spot, FinEnumerations.CalculationMode.AllGreeks);
        DerivativePricing.Tests.Utils.PrintResult(fusedAccumulatorGreeks);
    }
}

