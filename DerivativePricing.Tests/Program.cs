using BenchmarkDotNet.Running;
using DerivativePricing.Tests.Accumulator;
using DerivativePricing.Tests.Asian;
using DerivativePricing.Tests.Vanilla;
using DerivativePricing.Tests.Barrier;
using DerivativePricing.Tests.Binary;
using DerivativePricing.Tests.BinaryBarrier;
using DerivativePricing.Tests.FusedAccumulator;
using DerivativePricing.Tests.Simulator;
using DerivativePricing.Tests.Snowball;
using DerivativePricing.Tests.Spread;
using DerivativePricing.Tests.Util;

// var bivariateCdfTest = new BivariateCDFTest();
// bivariateCdfTest.TestBivariateCDF();


// var vanillaTest = new VanillaTest();
// vanillaTest.TestVanillaPricing();

// var barrierTest = new BarrierTest();
// barrierTest.TestBarrierPricing();

// var binaryTest = new BinaryTest();
// binaryTest.TestBinaryPricing();

// var accumulatorTest = new AccumulatorTest();
// accumulatorTest.TestAccumulatorPricing();

var spreadTest = new SpreadTest();
spreadTest.TestSpreadPricing();

// var binaryBarrierTest = new BinaryBarrierTest();
// binaryBarrierTest.TestBinaryBarrierPricing();

// var fusedAccumulatorTest = new FusedAccumulatorTest();
// fusedAccumulatorTest.TestFusedAccumulatorPricing();

// var asianTest = new AsianTest();
// asianTest.TestAsianPricing();

// var snowTest = new SnowballTest();
// snowTest.TestSnowballPricing();


// var simulatorTest = new DerivativePricing.Tests.Simulator.PathSimulatorTest();
// simulatorTest.TestPathSimulator();

// var utilsTest = new DerivativePricing.Tests.Util.UtilTest();
// utilsTest.TestUtils();


// performance test

// BenchmarkRunner.Run<BivariateCDFBenchmark>();

// BenchmarkRunner.Run<NormalCdfBenchmark>();

// BenchmarkRunner.Run<CustomizedNormalCdfBenchmark>();

// vanilla performance test
// var summary = BenchmarkRunner.Run<VanillaPerformanceTest>();


// barrier performance test
// var summary = BenchmarkRunner.Run<BarrierPerformance>();

// accumulator performance test
// var summary = BenchmarkRunner.Run<AccumulatorPerformanceTest>();

// spread option performance test 
// BenchmarkRunner.Run<SpreadPerformance>();

//fused accumulator performance test
// BenchmarkRunner.Run<FusedAccumulatorPerformance>();

// path simulator performance test
// BenchmarkRunner.Run<PathSimulatorPerformance>();

// utils performance test
// BenchmarkRunner.Run<UtilsBenchmarks>();

// snowball performance test
// var summary = BenchmarkRunner.Run<SnowballPerformanceTest>();
