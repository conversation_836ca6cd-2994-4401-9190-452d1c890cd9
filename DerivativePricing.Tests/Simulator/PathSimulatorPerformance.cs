using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Running;
using DerivativePricing.Core.Internals;

namespace DerivativePricing.Tests.Simulator;

[MemoryDiagnoser]
public class PathSimulatorPerformance
{
    private double s0 = 100.0;
    private double mu = 0.05;
    private double sigma = 0.2;
    private double dt = 1.0 / 243;
    private int pathNums = 1 << 16;
    private int stepNums = 486;
    
    [Benchmark]
    public double[] SimulatePaths()
    {
        return PathSimulator.Simulate(s0, mu, sigma, dt, pathNums, stepNums);
    }

    
    [Benchmark]
    public double[] SimulateCandidate1()
    {
        return PathSimulator.SimulateCandidate1(s0, mu, sigma, dt, pathNums, stepNums);
    }
    
}

// To run the benchmark, add a Main method in a Program.cs file:
// BenchmarkRunner.Run<PathSimulatorPerformance>();