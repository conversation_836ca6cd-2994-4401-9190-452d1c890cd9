using DerivativePricing.Core.Internals;

namespace DerivativePricing.Tests.Simulator;

public class PathSimulatorTest
{
    public void TestPathSimulator()
    {
        var pathNums = 2;
        var stepNums = 10;
        var arch = "CPU";
        var mu = 0.05;
        var sigma = 0.2;
        var s0 = 100.0;
        var dt = 1.0 / 243;
        var paths = PathSimulator.Simulate(s0,mu,sigma,dt,pathNums,stepNums);

    }

}
