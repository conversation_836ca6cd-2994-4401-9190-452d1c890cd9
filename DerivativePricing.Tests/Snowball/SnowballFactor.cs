using DerivativePricing.Domain.Models;
using FinEnumerations;
using QLNet;

namespace DerivativePricing.Tests.Snowball;

public static class SnowballFactor
{
    public static SnowballOption CreateSnowball()
    {
        int[] koTradedaySequence = [0, 10, 15, 20, 25, 30, 34, 39, 44, 49, 54, 59, 64, 69, 74, 79, 84];
        int[] koCalendarSequence = [0, 14, 21, 28, 35, 42, 56, 63, 70, 77, 84, 91, 98, 105, 112, 119, 126];
        int[] koFixedCalendarSequence = [7, 14, 21, 28, 35, 42, 56, 63, 70, 77, 84, 91, 98, 105, 112, 119, 126];

        double strike = 100;
        double stepDown = 0.5 / 100;
        double koPct = 97.0 / 100;
        double coupon = 0.322;
        double kiBarrierPct = 1.2;
        double kiFloorPct = 1.1;
        double basis = 2.0 / 100;
        double swapRate = -2.0 / 100;
        double noKoKiCoupon = 5.0 / 100;
        var kiStart = 0;

        double lateCoupon = 0.05;
        int lateCouponStart = 8;

        List<double> stepDownList = Enumerable.Repeat(stepDown, koTradedaySequence.Length).ToList();
        stepDownList[0] = 0;
        stepDownList = stepDownList.Select((k, m) => k * m).ToList();
        stepDownList = stepDownList.Select(s => s + koPct).ToList();

        int daysLeft = koTradedaySequence.Last();
        int calendarDay = koCalendarSequence.Last();
        
        double[] couponArray = Enumerable.Repeat(coupon, koTradedaySequence.Length).ToArray();
        for (int i = lateCouponStart; i < couponArray.Length; i++)
        {
            couponArray[i] = lateCoupon;
        }
        
        
        var snowball = new SnowballOption
        {
            EnhanceSnowball = false,
            EnhanceParticipationRate = 0,
            Coupons = couponArray,
            AdvancePaymentRate = 1,
            SwapRate = swapRate,
            KnockType = KnockType.DownOut,
            NoKoKiCoupon = noKoKiCoupon,
            CalendarDaysLeft = calendarDay,
            KiStatus = false,
            KoBarriers = stepDownList.Select(s => s * 100).ToArray(),
            KoTradeDaySequence = koTradedaySequence,
            KoFixedCalendarSequence = koFixedCalendarSequence,
            KiPricePercentage = kiBarrierPct,
            KiFloorPercentage = kiFloorPct,
            PayWhenKi = true,
            RiskFreeRate = 0.03,
            DividendYield = 0,
            DaysToExpiration = daysLeft,
            AnnualDenominator = 243,
            ParticipationRate = 1,
            BasisDiscount = basis,
            UnderlyingType = SecType.EquityIndex,
            StrikePrice = strike,
            Volatility = 0.26,
            IntraDayFraction = 0,
            ContractType = ContractType.Put,
            ExerciseType = ExerciseType.EuropeanExercise,
            PricingMethod = PricingMethod.MonteCarlo,
            OptionType = OptionType.Snowball,
            SnowballType = SnowballType.ProtectedLadderSnowball,
            PathNums = 1 << 16
            
        };
        return snowball;
    }

}