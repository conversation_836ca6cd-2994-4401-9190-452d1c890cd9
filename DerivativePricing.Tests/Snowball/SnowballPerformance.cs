using BenchmarkDotNet.Attributes;
using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.Snowball;

[MemoryDiagnoser]
public class SnowballPerformanceTest
{
    private double _spotPrice;
    private SnowballOption _snowball;
    private SnowballPricer _pricer;

    [GlobalSetup]
    public void Setup()
    {
        _spotPrice = 100.0;
        _snowball = SnowballFactor.CreateSnowball();
        _pricer = new SnowballPricer();
    }

    [Benchmark]
    public DerivativeResult PriceSnowballOption()
    {
        return _pricer.CalculatePrice(_snowball, _spotPrice);
    }

    // [Benchmark]
    // public void PriceSnowballOptionInit()
    // {
    //     // Just initialize pricer and option, no pricing
    //     var pricer = new SnowballPricer();
    //     var snowball = SnowballFactor.CreateSnowball();
    // }

    [Benchmark]
    public DerivativeResult CalculateSnowballGreeks()
    {
        var snowball = SnowballFactor.CreateSnowball();
        return _pricer.CalculatePrice(snowball, 100.0, CalculationMode.AllGreeks);
    }
}