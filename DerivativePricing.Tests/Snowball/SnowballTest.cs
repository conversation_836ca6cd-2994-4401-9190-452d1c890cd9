using DerivativePricing.Core.Pricers;
using FinEnumerations;

namespace DerivativePricing.Tests.Snowball;

public class SnowballTest
{
    public void TestSnowballPricing()
    {
        var spot = 96;
        var snowball = SnowballFactor.CreateSnowball();
        var snowballPricer = new SnowballPricer();
        var snowGreeks = snowballPricer.CalculatePrice(snowball, spot, CalculationMode.AllGreeks);
        Utils.PrintResult(snowGreeks);
    }
}