using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.Spread;

public class SpreadFactor
{
    public static SpreadOption CreateSpread()
    {
        return new SpreadOption
        {
           
            Rho = 0.5,
            Volume = 1,
            Volume2 = 1,
            RiskFreeRate = 0.03,
            DividendYield = 0,
            DaysToExpiration = 22,
            AnnualDenominator = 243,
            ParticipationRate = 0,
            BasisDiscount = 0,
            BasisDiscount2 = 0,
            UnderlyingType = SecType.CommodityFutures,
            StrikePrice = 0,
            Volatility = 0.2,
            Volatility2 = 0.2,
            IntraDayFraction = 0,
            ContractType = ContractType.Call,
            ExerciseType = ExerciseType.EuropeanExercise,
            OptionType = OptionType.SpreadOption,
            UnderlyingType2 = SecType.CommodityFutures,
            PricingMethod = PricingMethod.Analytic,
        };
    }
}