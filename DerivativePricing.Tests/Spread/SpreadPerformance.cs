using BenchmarkDotNet.Attributes;
using DerivativePricing.Core.Pricers;
using FinEnumerations;

namespace DerivativePricing.Tests.Spread;

[MemoryDiagnoser]
public class SpreadPerformance
{
    private double[] _spots;
    private Domain.Models.SpreadOption _spread;

    [GlobalSetup]
    public void Setup()
    {
        _spots = new double[] { 3850, 3820 };
        _spread = SpreadFactor.CreateSpread();
    }

    [Benchmark]
    public void PriceSpread()
    {
        var pricer = new SpreadPricer();
        pricer.CalculatePrice(_spread, _spots);
    }

    [Benchmark]
    public void CalculateSpreadGreeks()
    {
        var pricer = new SpreadPricer();
        pricer.CalculatePrice(_spread, _spots, CalculationMode.AllGreeks);
    }
}