using DerivativePricing.Core.Pricers;
using FinEnumerations;

namespace DerivativePricing.Tests.Spread;

public class SpreadTest
{
    public void TestSpreadPricing()
    {
        var spots = new double[] { 100, 100 };
        var spread = SpreadFactor.CreateSpread();
        var pricer = new SpreadPricer();
        var spreadGreeks = pricer.CalculatePrice(spread, spots, CalculationMode.AllGreeks);
        Utils.PrintResult(spreadGreeks);
    }
}