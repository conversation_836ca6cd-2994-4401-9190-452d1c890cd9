using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Running;

namespace DerivativePricing.Tests.Util
{
    public class UtilsBenchmarks
    {
        private readonly double[] _target = new double[24]
        {
            1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24
        };
        private readonly double[] _compareBase = new double[24]
        {
            23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23
        };
        private readonly int[] _observationSeq = new int[24]
        {
            0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23
        };

        [Benchmark]
        public int FirstOccurrenceGreaterThanOrEqual()
        {
            return Core.Internals.Utils.FirstOccurrenceGreaterThanOrEqual(_target, _compareBase, _observationSeq);
        }

        [Benchmark]
        public int OptimizedSearch_FirstOccurrence()
        {
            return Core.Internals.Utils.OptimizedSearch.FirstOccurrence(_target, _compareBase, _observationSeq, (a, b) => a >= b);
        }
    }

    public class Program
    {
        public static void Main(string[] args)
        {
            BenchmarkRunner.Run<UtilsBenchmarks>();
        }
    }
}