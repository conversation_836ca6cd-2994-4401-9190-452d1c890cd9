namespace DerivativePricing.Tests.Util;

public class UtilTest
{
    public void TestUtils()
    {
        
        var target = new double[24]
        {
            1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24
        };
        var compareBase = new double[24]
        {
            23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23
        };
        var observationSeq = new int[24]
        {
            0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23
        };
        // Example usage of Utils.FirstOccurrenceGreaterThanOrEqual
        int result = Core.Internals.Utils.FirstOccurrenceGreaterThanOrEqual(target, compareBase, observationSeq);
        System.Console.WriteLine($"First occurrence greater than or equal: {result}");

        // Example usage of Utils.OptimizedSearch.FirstOccurrence
        int optimizedResult = Core.Internals.Utils.OptimizedSearch.FirstOccurrence(target, compareBase, observationSeq, (a, b) => a >= b);
        System.Console.WriteLine($"Optimized search first occurrence: {optimizedResult}");
    }
    
}