using System.Globalization;
using Spectre.Console;
using DerivativePricing.Domain.Models;

namespace DerivativePricing.Tests;

public static class Utils
{
    // public static void PrintResult(DerivativeResult result)
    // {
    //     foreach (var property in result.GetType().GetProperties())
    //     {
    //         bool isListOfDouble = property.PropertyType.IsGenericType &&
    //                               property.PropertyType.GetGenericTypeDefinition() == typeof(List<>) &&
    //                               property.PropertyType.GenericTypeArguments[0] == typeof(double);
    //         if (isListOfDouble)
    //         {
    //             var numbers = (List<double>)property.GetValue(result)!;
    //             var name = property.Name;
    //             var formatted = string.Join(", ", numbers.Select(n=> n.ToString(CultureInfo.InvariantCulture)));
    //             // Console.WriteLine($"{name}: [{formatted}]")
    //             AnsiConsole.MarkupLine($"{name}: [[{formatted}]]");
    //             continue;
    //         }
    //         AnsiConsole.MarkupLine($"{property.Name}: {property.GetValue(result)}");
    //     }
    // }
    
    public static void PrintResult(DerivativeResult result)
    {
        foreach (var property in result.GetType().GetProperties())
        {
            object? value = property.GetValue(result);
            string name = property.Name;

            if (value is double[] arr)
            {
                var formatted = string.Join(", ", arr.Select(n => n.ToString(CultureInfo.InvariantCulture)));
                AnsiConsole.MarkupLine($"{name}: [[{formatted}]]");
                continue;
            }
            if (value is List<double> list)
            {
                var formatted = string.Join(", ", list.Select(n => n.ToString(CultureInfo.InvariantCulture)));
                AnsiConsole.MarkupLine($"{name}: [[{formatted}]]");
                continue;
            }
            // Escape brackets in string values if needed
            var strValue = value?.ToString()?.Replace("[", "[[").Replace("]", "]]");
            AnsiConsole.MarkupLine($"{name}: {strValue}");
        }
    }
}