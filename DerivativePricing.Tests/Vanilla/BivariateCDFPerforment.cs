using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Running;
using DerivativePricing.Core.Internals;

public class BivariateCDFBenchmark
{
    [Params(0.0, 0.5, 0.9)]
    public double Rho;

    [Params(-2.0, 0.0, 2.0)]
    public double X;

    [Params(-2.0, 0.0, 2.0)]
    public double Y;

    [Benchmark]
    public double ComputeBivariateCDF()
    {
        return Utils.BivariateCDF_SIMD(X, Y, Rho);
    }
}

// To run the benchmark, add the following Main method in a Program.cs file:
// BenchmarkRunner.Run<BivariateCDFBenchmark>();