using DerivativePricing.Core.Internals;

namespace DerivativePricing.Tests.Vanilla;


public class BivariateCDFTest
{
    public void TestBivariateCDF()
    {
        var x = 0.0;
        var y = 0.5;
        var rho = -0.5;

        // Calculate the bivariate CDF using the BivariateNormal class
        var bivariateCdf = Core.Internals.Utils.BivariateCDF_SIMD(x, y, rho);

        // Print the result
        Console.WriteLine($"Bivariate CDF for x={x}, y={y}, rho={rho}: {bivariateCdf}");
    }
}