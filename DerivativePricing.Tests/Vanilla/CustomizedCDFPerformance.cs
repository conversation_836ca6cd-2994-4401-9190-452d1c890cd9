using BenchmarkDotNet.Attributes;
using DerivativePricing.Core.Internals;


public class CustomizedNormalCdfBenchmark
{
    [Params(-3.0, 0.0, 3.0, 10.0)]
    public double X;

    [Benchmark]
    public double ComputeNormalCdf()
    {
        return Utils.NormalCDF(X);
    }
}

// To run the benchmark, add the following Main method in a Program.cs file:
// BenchmarkRunner.Run<CustomizedNormalCdfBenchmark>();