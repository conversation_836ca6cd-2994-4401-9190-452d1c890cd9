using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Running;
using MathNet.Numerics.Distributions;

public class NormalCdfBenchmark
{
    [Params(-3.0, 0.0, 3.0, 10.0)]
    public double X;

    [Benchmark]
    public double ComputeNormalCdf()
    {
        return Normal.CDF(0, 1, X);
    }
}

// To run the benchmark, add the following Main method in a Program.cs file:
// BenchmarkRunner.Run<NormalCdfBenchmark>();