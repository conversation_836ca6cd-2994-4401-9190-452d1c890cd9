using DerivativePricing.Domain.Models;
using FinEnumerations;

namespace DerivativePricing.Tests.Vanilla;

public static class VanillaFactor
{
    public static VanillaOption CreateVanilla()
    {
        var vanilla = new VanillaOption
        {
            StrikePrice = 100,
            Volatility = 0.15,
            RiskFreeRate = 0.1,
            DividendYield = 0.0,
            BasisDiscount = -0.0,
            DaysToExpiration = 24,
            IntraDayFraction = 0,
            ContractType = ContractType.Call,
            PricingMethod = PricingMethod.Analytic,
            UnderlyingType = SecType.CommodityFutures,
            ExerciseType = ExerciseType.AmericanExercise,
            OptionType = OptionType.Vanilla,
            AnnualDenominator = 240,
            ParticipationRate = 1
        };
        return vanilla;
    }
    
}

