using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Running;
using DerivativePricing.Core.Pricers;
using DerivativePricing.Domain.Models;
using FinEnumerations;


namespace DerivativePricing.Tests.Vanilla;

[MemoryDiagnoser]
public class VanillaPerformanceTest
{
    private double _spotPrice;

    [GlobalSetup]
    public void Setup()
    {
        // _vanillaOption = VanillaFactor.CreateVanilla();

        _spotPrice = 100.0;
    }

    [Benchmark]
    public DerivativeResult PriceVanillaOption()
    {
        var pricer = new VanillaPricer();
        var vanilla = VanillaFactor.CreateVanilla();
        return pricer.CalculatePrice(vanilla, _spotPrice);
    }

    [Benchmark]
    public void PriceVanillaOptionInit()
    {
        var pricer = new VanillaPricer();
        // var vanilla = VanillaFactor.CreateVanilla();
        // return pricer.CalculatePrice(vanilla, _spotPrice);
    }

    [Benchmark]
    public DerivativeResult CalculateVanillaGreeks()
    {
        var pricer = new VanillaPricer();
        var vanilla = VanillaFactor.CreateVanilla();
        return pricer.CalculatePrice(vanilla, _spotPrice, CalculationMode.AllGreeks);
    }
}