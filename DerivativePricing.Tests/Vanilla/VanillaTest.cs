using DerivativePricing.Core.Pricers;
using FinEnumerations;


namespace DerivativePricing.Tests.Vanilla;

public class VanillaTest
{
    public void TestVanillaPricing()
    {
        var spot = 110;
        var vanilla = VanillaFactor.CreateVanilla();
        // vanilla.ExerciseType = ExerciseType.AmericanExercise;

        var vanillaPricer = new VanillaPricer();
        // var vanillaPrice = vanillaPricer.CalculatePrice(vanilla, spot);
        // var vanillaGreeks = VanillaPricer.CalculateGreeks(vanilla, spot, true);
        var vanillaGreeks = vanillaPricer.CalculatePrice(vanilla, spot, CalculationMode.AllGreeks);
        Utils.PrintResult(vanillaGreeks);
    }
}